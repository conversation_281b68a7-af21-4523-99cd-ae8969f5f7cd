#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装pyannote-audio说话人分离工具
"""

import subprocess
import sys
import os

def install_pyannote():
    """安装pyannote-audio及其依赖"""
    
    print("=" * 70)
    print("安装pyannote-audio说话人分离工具")
    print("=" * 70)
    
    packages = [
        "pyannote.audio",
        "pyannote.core", 
        "pyannote.database",
        "pyannote.metrics",
        "torch",
        "torchaudio",
        "librosa",
        "soundfile",
        "matplotlib",
        "seaborn"
    ]
    
    for package in packages:
        print(f"\n安装 {package}...")
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"✓ {package} 安装成功")
            else:
                print(f"✗ {package} 安装失败:")
                print(result.stderr)
        except subprocess.TimeoutExpired:
            print(f"⚠️ {package} 安装超时")
        except Exception as e:
            print(f"✗ {package} 安装出错: {e}")
    
    print("\n" + "=" * 70)
    print("安装完成！")
    print("=" * 70)

def test_imports():
    """测试导入"""
    print("\n测试导入...")
    
    try:
        import torch
        print(f"✓ PyTorch: {torch.__version__}")
    except ImportError as e:
        print(f"✗ PyTorch导入失败: {e}")
    
    try:
        import torchaudio
        print(f"✓ TorchAudio: {torchaudio.__version__}")
    except ImportError as e:
        print(f"✗ TorchAudio导入失败: {e}")
    
    try:
        from pyannote.audio import Pipeline
        print("✓ pyannote.audio导入成功")
    except ImportError as e:
        print(f"✗ pyannote.audio导入失败: {e}")
    
    try:
        import librosa
        print(f"✓ librosa: {librosa.__version__}")
    except ImportError as e:
        print(f"✗ librosa导入失败: {e}")

if __name__ == "__main__":
    install_pyannote()
    test_imports()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合的ASR系统
结合说话人分离和语音识别，只转录主要说话人（录音设备主人）的内容
"""

import os
import sys
import logging
import warnings
import numpy as np
import json
from datetime import datetime

# 抑制警告
warnings.filterwarnings("ignore")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入说话人分离系统
from speaker_diarization_system import SpeakerDiarizationSystem

# 导入Whisper
try:
    from faster_whisper import WhisperModel
    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False

class IntegratedASRSystem:
    """整合的ASR系统"""

    def __init__(self, whisper_model="large-v2"):
        self.whisper_model_name = whisper_model
        self.whisper_model = None
        self.speaker_system = None

        logger.info(f"初始化整合ASR系统 (Whisper: {whisper_model})")

    def initialize(self):
        """初始化系统组件"""
        try:
            # 1. 初始化说话人分离系统
            logger.info("初始化说话人分离系统...")
            self.speaker_system = SpeakerDiarizationSystem()
            if not self.speaker_system.initialize():
                raise RuntimeError("说话人分离系统初始化失败")

            # 2. 初始化Whisper模型
            logger.info(f"初始化Whisper模型: {self.whisper_model_name}")
            if not WHISPER_AVAILABLE:
                raise ImportError("Whisper不可用")

            # 使用本地路径加载large-v2
            if self.whisper_model_name == "large-v2":
                import os
                model_path = os.path.expanduser("~/.cache/huggingface/hub/models--Systran--faster-whisper-large-v2")
                if os.path.exists(model_path):
                    logger.info("使用本地large-v2模型")
                    self.whisper_model = WhisperModel(
                        model_path,
                        device="cpu",
                        compute_type="int8",
                        cpu_threads=4
                    )
                else:
                    logger.warning("本地large-v2不存在，使用在线下载")
                    self.whisper_model = WhisperModel(
                        self.whisper_model_name,
                        device="cpu",
                        compute_type="int8",
                        cpu_threads=4
                    )
            else:
                self.whisper_model = WhisperModel(
                    self.whisper_model_name,
                    device="cpu",
                    compute_type="int8",
                    cpu_threads=4
                )

            logger.info("整合ASR系统初始化成功")
            return True

        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            return False

    def extract_main_speaker_audio(self, audio_path, main_speaker_segments):
        """提取主要说话人的音频片段"""
        try:
            from audio_loader import load_audio

            # 加载完整音频
            wav = load_audio(audio_path, sr=16000)

            # 提取主要说话人片段
            main_speaker_audio_segments = []

            for start_time, end_time in main_speaker_segments:
                start_sample = int(start_time * 16000)
                end_sample = int(end_time * 16000)

                # 确保索引在有效范围内
                start_sample = max(0, start_sample)
                end_sample = min(len(wav), end_sample)

                if end_sample > start_sample:
                    segment_audio = wav[start_sample:end_sample]
                    main_speaker_audio_segments.append({
                        'audio': segment_audio,
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration': end_time - start_time
                    })

            logger.info(f"提取了 {len(main_speaker_audio_segments)} 个主要说话人音频片段")

            return main_speaker_audio_segments

        except Exception as e:
            logger.error(f"音频片段提取失败: {e}")
            return []

    def transcribe_segments(self, audio_segments):
        """转录音频片段"""
        try:
            logger.info("开始转录主要说话人音频片段...")

            transcribed_segments = []

            for i, segment in enumerate(audio_segments):
                logger.info(f"转录片段 {i+1}/{len(audio_segments)}: {segment['start_time']:.1f}s-{segment['end_time']:.1f}s")

                # 保存临时音频文件
                temp_audio_path = f"temp_segment_{i}.wav"

                try:
                    import soundfile as sf
                    sf.write(temp_audio_path, segment['audio'], 16000)

                    # 使用Whisper转录
                    segments, info = self.whisper_model.transcribe(
                        temp_audio_path,
                        language="zh",
                        beam_size=5,
                        best_of=5,
                        temperature=[0.0, 0.2, 0.4, 0.6, 0.8, 1.0],
                        word_timestamps=True,
                        vad_filter=True,
                        vad_parameters=dict(
                            min_silence_duration_ms=300,
                            speech_pad_ms=400,
                            max_speech_duration_s=30
                        ),
                        condition_on_previous_text=True,
                        compression_ratio_threshold=2.4,
                        log_prob_threshold=-1.0,
                        no_speech_threshold=0.6
                    )

                    # 收集转录结果
                    segment_text = ""
                    segment_words = []

                    for whisper_segment in segments:
                        segment_text += whisper_segment.text

                        if hasattr(whisper_segment, 'words') and whisper_segment.words:
                            for word in whisper_segment.words:
                                segment_words.append({
                                    'word': word.word.strip(),
                                    'start': segment['start_time'] + word.start,
                                    'end': segment['start_time'] + word.end,
                                    'probability': word.probability
                                })

                    transcribed_segment = {
                        'start_time': segment['start_time'],
                        'end_time': segment['end_time'],
                        'duration': segment['duration'],
                        'text': segment_text.strip(),
                        'language': info.language,
                        'language_probability': info.language_probability,
                        'words': segment_words
                    }

                    transcribed_segments.append(transcribed_segment)

                    logger.info(f"片段转录完成: {segment_text.strip()}")

                except Exception as e:
                    logger.error(f"片段 {i} 转录失败: {e}")

                    # 添加空转录结果
                    transcribed_segments.append({
                        'start_time': segment['start_time'],
                        'end_time': segment['end_time'],
                        'duration': segment['duration'],
                        'text': '',
                        'error': str(e)
                    })

                finally:
                    # 清理临时文件
                    if os.path.exists(temp_audio_path):
                        os.remove(temp_audio_path)

            logger.info(f"转录完成，共 {len(transcribed_segments)} 个片段")

            return transcribed_segments

        except Exception as e:
            logger.error(f"转录失败: {e}")
            return []

    def process_audio(self, audio_path):
        """处理音频文件"""
        try:
            logger.info(f"开始处理音频文件: {audio_path}")
            start_time = datetime.now()

            # 1. 说话人分离
            logger.info("步骤1: 说话人分离...")
            speaker_result = self.speaker_system.process_audio(audio_path)
            if not speaker_result:
                raise RuntimeError("说话人分离失败")

            # 2. 提取主要说话人音频
            logger.info("步骤2: 提取主要说话人音频...")
            main_speaker_segments = speaker_result['main_speaker_segments']
            audio_segments = self.extract_main_speaker_audio(audio_path, main_speaker_segments)

            if not audio_segments:
                raise RuntimeError("没有提取到主要说话人音频")

            # 3. 转录主要说话人音频
            logger.info("步骤3: 转录主要说话人音频...")
            transcribed_segments = self.transcribe_segments(audio_segments)

            # 4. 整理结果
            processing_time = (datetime.now() - start_time).total_seconds()

            # 生成完整文本
            full_text = ' '.join([seg['text'] for seg in transcribed_segments if seg.get('text')])

            # 计算统计信息
            total_main_speaker_duration = sum([seg['duration'] for seg in transcribed_segments])
            total_words = sum([len(seg['text'].split()) for seg in transcribed_segments if seg.get('text')])

            result = {
                'system_info': {
                    'version': '整合ASR系统 v1.0',
                    'whisper_model': self.whisper_model_name,
                    'processing_time': processing_time,
                    'timestamp': datetime.now().isoformat()
                },
                'audio_info': {
                    'file_path': audio_path,
                    'total_duration': speaker_result['audio_duration'],
                    'main_speaker_duration': total_main_speaker_duration,
                    'main_speaker_percentage': (total_main_speaker_duration / speaker_result['audio_duration']) * 100
                },
                'speaker_diarization': {
                    'num_speakers': speaker_result['num_speakers'],
                    'main_speaker': speaker_result['main_speaker'],
                    'speaker_durations': speaker_result['speaker_durations'],
                    'main_speaker_segments': main_speaker_segments
                },
                'transcription': {
                    'segments': transcribed_segments,
                    'segment_count': len(transcribed_segments),
                    'total_words': total_words,
                    'full_text': full_text
                }
            }

            logger.info(f"处理完成，用时 {processing_time:.2f} 秒")

            return result

        except Exception as e:
            logger.error(f"音频处理失败: {e}")
            return None

def main():
    """主函数"""
    print("=" * 80)
    print("整合的机场地勤ASR系统 (pyannote + Whisper Large-v3)")
    print("pyannote说话人分离 + 主要说话人ASR")
    print("=" * 80)

    # 检查音频文件
    audio_file = "../data/机场地勤音频.WAV"
    if not os.path.exists(audio_file):
        print(f"错误: 音频文件不存在 - {audio_file}")
        return

    # 创建整合系统 - 使用pyannote + Whisper Large-v3
    asr_system = IntegratedASRSystem(whisper_model="large-v3")

    # 初始化
    if not asr_system.initialize():
        print("系统初始化失败")
        return

    # 处理音频
    result = asr_system.process_audio(audio_file)

    if result:
        # 显示结果
        print(f"\n处理结果:")
        print(f"音频文件: {result['audio_info']['file_path']}")
        print(f"总时长: {result['audio_info']['total_duration']:.2f} 秒")
        print(f"主要说话人时长: {result['audio_info']['main_speaker_duration']:.2f} 秒")
        print(f"主要说话人占比: {result['audio_info']['main_speaker_percentage']:.1f}%")
        print(f"处理时间: {result['system_info']['processing_time']:.2f} 秒")

        print(f"\n说话人分离:")
        print(f"检测到说话人数: {result['speaker_diarization']['num_speakers']}")
        print(f"主要说话人: {result['speaker_diarization']['main_speaker']}")

        print(f"\n转录结果:")
        print(f"有效片段数: {result['transcription']['segment_count']}")
        print(f"总词数: {result['transcription']['total_words']}")

        if result['transcription']['segments']:
            print(f"\n主要说话人详细转录:")
            print("-" * 60)
            for i, segment in enumerate(result['transcription']['segments'], 1):
                if segment.get('text'):
                    print(f"{i:2d}. [{segment['start_time']:6.1f}s - {segment['end_time']:6.1f}s] {segment['text']}")
            print("-" * 60)

            print(f"\n主要说话人完整文本:")
            print("=" * 60)
            print(result['transcription']['full_text'])
            print("=" * 60)

        # 保存结果
        output_file = f"integrated_asr_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        print(f"\n详细结果已保存到: {output_file}")

    else:
        print("处理失败")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
说话人分离系统
使用resemblyzer和librosa实现真正的说话人分离
"""

import os
import sys
import logging
import warnings
import numpy as np
import json
from datetime import datetime
from pathlib import Path

# 抑制警告
warnings.filterwarnings("ignore")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入依赖
try:
    from resemblyzer import preprocess_wav, VoiceEncoder
    import librosa
    import soundfile as sf
    from sklearn.cluster import AgglomerativeClustering
    from sklearn.metrics import silhouette_score
    import matplotlib.pyplot as plt
    import webrtcvad
    SPEAKER_DIARIZATION_AVAILABLE = True
    logger.info("说话人分离工具可用")
except ImportError as e:
    SPEAKER_DIARIZATION_AVAILABLE = False
    logger.error(f"说话人分离工具导入失败: {e}")

# 导入Whisper
try:
    from faster_whisper import WhisperModel
    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False

class SpeakerDiarizationSystem:
    """说话人分离系统"""

    def __init__(self):
        self.voice_encoder = None
        self.sample_rate = 16000
        self.window_size = 1.6  # 1.6秒窗口
        self.step_size = 0.8    # 0.8秒步长

        logger.info("初始化说话人分离系统")

    def initialize(self):
        """初始化语音编码器"""
        try:
            if not SPEAKER_DIARIZATION_AVAILABLE:
                raise ImportError("说话人分离工具不可用")

            logger.info("加载语音编码器...")
            self.voice_encoder = VoiceEncoder()
            logger.info("语音编码器加载成功")
            return True

        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False

    def preprocess_audio(self, audio_path):
        """预处理音频"""
        try:
            logger.info(f"预处理音频: {audio_path}")

            # 导入通用音频加载器
            try:
                from audio_loader import load_audio
                wav = load_audio(audio_path, sr=self.sample_rate)
                logger.info("使用通用音频加载器成功")
            except ImportError:
                # 备用方法1：使用librosa
                logger.info("使用librosa加载音频...")
                wav, sr = librosa.load(audio_path, sr=self.sample_rate)

            # 标准化音频
            if np.max(np.abs(wav)) > 0:
                wav = wav / np.max(np.abs(wav))

            # 获取音频信息
            duration = len(wav) / self.sample_rate

            logger.info(f"音频预处理完成，时长: {duration:.2f}秒")

            return wav, duration

        except Exception as e:
            logger.error(f"音频预处理失败: {e}")
            # 备用方法2：尝试使用resemblyzer
            try:
                logger.info("尝试使用resemblyzer预处理...")
                wav = preprocess_wav(audio_path)
                duration = len(wav) / self.sample_rate
                logger.info(f"备用方法成功，时长: {duration:.2f}秒")
                return wav, duration
            except Exception as e2:
                logger.error(f"备用方法也失败: {e2}")
                return None, 0

    def extract_speaker_embeddings(self, wav):
        """提取说话人嵌入向量"""
        try:
            logger.info("提取说话人嵌入向量...")

            # 确保wav是1维数组
            if wav.ndim > 1:
                wav = wav.flatten()

            # 使用滑动窗口提取嵌入
            embeddings = []
            timestamps = []

            window_samples = int(self.window_size * self.sample_rate)
            step_samples = int(self.step_size * self.sample_rate)

            # 确保窗口大小合理
            if window_samples > len(wav):
                logger.warning(f"音频太短，调整窗口大小: {len(wav)} samples")
                window_samples = len(wav) // 2
                step_samples = window_samples // 2

            for start in range(0, len(wav) - window_samples + 1, step_samples):
                end = start + window_samples
                window_wav = wav[start:end]

                # 检查窗口是否包含足够的语音
                if np.max(np.abs(window_wav)) > 0.01:  # 简单的语音检测
                    try:
                        # 确保窗口长度符合要求
                        if len(window_wav) >= self.sample_rate * 0.5:  # 至少0.5秒
                            embedding = self.voice_encoder.embed_utterance(window_wav)
                            embeddings.append(embedding)
                            timestamps.append((start / self.sample_rate, end / self.sample_rate))
                    except Exception as e:
                        logger.warning(f"跳过窗口 {start}-{end}: {e}")
                        continue

            if not embeddings:
                logger.error("没有提取到任何嵌入向量")
                return None, None

            embeddings = np.array(embeddings)

            logger.info(f"提取了 {len(embeddings)} 个嵌入向量")

            return embeddings, timestamps

        except Exception as e:
            logger.error(f"嵌入向量提取失败: {e}")
            return None, None

    def cluster_speakers(self, embeddings, max_speakers=5):
        """聚类识别说话人"""
        try:
            logger.info("进行说话人聚类...")

            if len(embeddings) < 2:
                logger.warning("嵌入向量太少，无法聚类")
                return np.zeros(len(embeddings))

            # 尝试不同的聚类数量
            best_score = -1
            best_labels = None
            best_n_clusters = 2

            for n_clusters in range(2, min(max_speakers + 1, len(embeddings))):
                clustering = AgglomerativeClustering(
                    n_clusters=n_clusters,
                    linkage='ward'
                )

                labels = clustering.fit_predict(embeddings)

                # 计算轮廓系数
                if len(set(labels)) > 1:
                    score = silhouette_score(embeddings, labels)

                    if score > best_score:
                        best_score = score
                        best_labels = labels
                        best_n_clusters = n_clusters

            if best_labels is None:
                # 如果聚类失败，假设只有一个说话人
                best_labels = np.zeros(len(embeddings))
                best_n_clusters = 1

            logger.info(f"识别出 {best_n_clusters} 个说话人，轮廓系数: {best_score:.3f}")

            return best_labels

        except Exception as e:
            logger.error(f"说话人聚类失败: {e}")
            return np.zeros(len(embeddings))

    def identify_main_speaker(self, labels, timestamps):
        """识别主要说话人（说话时间最长）"""
        try:
            # 计算每个说话人的总时长
            speaker_durations = {}

            for i, (start, end) in enumerate(timestamps):
                speaker_id = labels[i]
                duration = end - start

                if speaker_id not in speaker_durations:
                    speaker_durations[speaker_id] = 0
                speaker_durations[speaker_id] += duration

            # 找出说话时间最长的说话人
            main_speaker = max(speaker_durations.keys(), key=speaker_durations.get)

            logger.info("说话人时长统计:")
            for speaker_id, duration in speaker_durations.items():
                status = " (主要说话人)" if speaker_id == main_speaker else ""
                logger.info(f"  说话人 {speaker_id}: {duration:.2f}秒{status}")

            return main_speaker, speaker_durations

        except Exception as e:
            logger.error(f"主要说话人识别失败: {e}")
            return 0, {}

    def create_speaker_segments(self, labels, timestamps, main_speaker):
        """创建主要说话人的时间段"""
        try:
            main_speaker_segments = []

            # 合并连续的主要说话人片段
            current_start = None
            current_end = None

            for i, (start, end) in enumerate(timestamps):
                if labels[i] == main_speaker:
                    if current_start is None:
                        current_start = start
                        current_end = end
                    elif start - current_end <= 1.0:  # 如果间隔小于1秒，合并
                        current_end = end
                    else:
                        # 保存当前片段，开始新片段
                        main_speaker_segments.append((current_start, current_end))
                        current_start = start
                        current_end = end
                else:
                    # 非主要说话人，结束当前片段
                    if current_start is not None:
                        main_speaker_segments.append((current_start, current_end))
                        current_start = None
                        current_end = None

            # 保存最后一个片段
            if current_start is not None:
                main_speaker_segments.append((current_start, current_end))

            logger.info(f"主要说话人有 {len(main_speaker_segments)} 个连续片段")

            return main_speaker_segments

        except Exception as e:
            logger.error(f"创建说话人片段失败: {e}")
            return []

    def process_audio(self, audio_path):
        """处理音频文件，返回主要说话人的时间段"""
        try:
            # 1. 预处理音频
            wav, duration = self.preprocess_audio(audio_path)
            if wav is None:
                return None

            # 2. 提取嵌入向量
            embeddings, timestamps = self.extract_speaker_embeddings(wav)
            if embeddings is None:
                return None

            # 3. 聚类识别说话人
            labels = self.cluster_speakers(embeddings)

            # 4. 识别主要说话人
            main_speaker, speaker_durations = self.identify_main_speaker(labels, timestamps)

            # 5. 创建主要说话人片段
            main_speaker_segments = self.create_speaker_segments(labels, timestamps, main_speaker)

            result = {
                'audio_duration': float(duration),
                'total_segments': int(len(timestamps)),
                'num_speakers': int(len(set(labels))),
                'main_speaker': int(main_speaker),
                'speaker_durations': {int(k): float(v) for k, v in speaker_durations.items()},
                'main_speaker_segments': [(float(start), float(end)) for start, end in main_speaker_segments],
                'all_segments': [
                    {
                        'start': float(start),
                        'end': float(end),
                        'speaker': int(labels[i]),
                        'is_main_speaker': bool(labels[i] == main_speaker)
                    }
                    for i, (start, end) in enumerate(timestamps)
                ]
            }

            return result

        except Exception as e:
            logger.error(f"音频处理失败: {e}")
            return None

def test_speaker_diarization():
    """测试说话人分离系统"""
    print("=" * 70)
    print("测试说话人分离系统")
    print("=" * 70)

    # 检查音频文件
    audio_file = "机场地勤音频.WAV"
    if not os.path.exists(audio_file):
        print(f"错误: 音频文件不存在 - {audio_file}")
        return

    # 创建说话人分离系统
    diarization_system = SpeakerDiarizationSystem()

    # 初始化
    if not diarization_system.initialize():
        print("说话人分离系统初始化失败")
        return

    print(f"开始处理音频文件: {audio_file}")
    start_time = datetime.now()

    # 处理音频
    result = diarization_system.process_audio(audio_file)

    processing_time = (datetime.now() - start_time).total_seconds()

    if result:
        print(f"\n说话人分离结果:")
        print(f"音频时长: {result['audio_duration']:.2f} 秒")
        print(f"处理时间: {processing_time:.2f} 秒")
        print(f"检测到说话人数: {result['num_speakers']}")
        print(f"主要说话人: {result['main_speaker']}")

        print(f"\n说话人时长分布:")
        for speaker_id, duration in result['speaker_durations'].items():
            percentage = (duration / result['audio_duration']) * 100
            status = " (主要说话人)" if speaker_id == result['main_speaker'] else ""
            print(f"  说话人 {speaker_id}: {duration:.2f}秒 ({percentage:.1f}%){status}")

        print(f"\n主要说话人连续片段:")
        for i, (start, end) in enumerate(result['main_speaker_segments'], 1):
            duration = end - start
            print(f"  片段 {i}: {start:.1f}s - {end:.1f}s (时长: {duration:.1f}s)")

        # 保存结果
        output_file = f"speaker_diarization_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        print(f"\n详细结果已保存到: {output_file}")

        return result
    else:
        print("说话人分离失败")
        return None

if __name__ == "__main__":
    test_speaker_diarization()

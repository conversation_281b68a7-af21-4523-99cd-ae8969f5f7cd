#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解决sentencepiece编译问题
"""

import subprocess
import sys
import os
import platform

def run_command(cmd, timeout=300):
    """运行命令"""
    try:
        print(f"执行: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        print(f"命令执行失败: {e}")
        return False, "", str(e)

def install_build_tools():
    """安装编译工具"""
    print("=" * 60)
    print("安装编译工具和依赖")
    print("=" * 60)
    
    system = platform.system()
    
    if system == "Darwin":  # macOS
        print("检测到macOS系统")
        
        # 安装Xcode命令行工具
        print("安装Xcode命令行工具...")
        success, _, _ = run_command(["xcode-select", "--install"])
        if not success:
            print("Xcode命令行工具可能已安装")
        
        # 使用Homebrew安装依赖
        homebrew_packages = [
            "pkg-config",
            "protobuf", 
            "sentencepiece",
            "cmake",
            "make"
        ]
        
        for package in homebrew_packages:
            print(f"安装 {package}...")
            success, stdout, stderr = run_command(["brew", "install", package])
            if success:
                print(f"✓ {package}")
            else:
                print(f"⚠️ {package}: {stderr[:100]}...")
    
    elif system == "Linux":
        print("检测到Linux系统")
        
        # Ubuntu/Debian
        packages = [
            "build-essential",
            "cmake", 
            "pkg-config",
            "libprotobuf-dev",
            "protobuf-compiler",
            "libsentencepiece-dev",
            "sentencepiece"
        ]
        
        for package in packages:
            print(f"安装 {package}...")
            success, _, _ = run_command(["sudo", "apt-get", "install", "-y", package])
            if success:
                print(f"✓ {package}")
            else:
                print(f"⚠️ {package} 安装失败")

def fix_sentencepiece_method_1():
    """方法1: 使用预编译的wheel"""
    print("\n方法1: 安装预编译的sentencepiece")
    
    # 先尝试安装预编译版本
    success, _, stderr = run_command([
        sys.executable, "-m", "pip", "install", 
        "--only-binary=all", "sentencepiece"
    ])
    
    if success:
        print("✓ 预编译sentencepiece安装成功")
        return True
    else:
        print(f"✗ 预编译版本失败: {stderr[:200]}...")
        return False

def fix_sentencepiece_method_2():
    """方法2: 使用conda安装"""
    print("\n方法2: 使用conda安装sentencepiece")
    
    # 检查conda
    success, _, _ = run_command(["conda", "--version"])
    if not success:
        print("conda不可用")
        return False
    
    success, _, stderr = run_command([
        "conda", "install", "-c", "conda-forge", "sentencepiece", "-y"
    ])
    
    if success:
        print("✓ conda sentencepiece安装成功")
        return True
    else:
        print(f"✗ conda安装失败: {stderr[:200]}...")
        return False

def fix_sentencepiece_method_3():
    """方法3: 设置环境变量后编译"""
    print("\n方法3: 设置环境变量后编译安装")
    
    # 设置环境变量
    env = os.environ.copy()
    
    # macOS特定设置
    if platform.system() == "Darwin":
        # 设置编译器路径
        env["CC"] = "clang"
        env["CXX"] = "clang++"
        
        # 设置pkg-config路径
        homebrew_prefix = "/usr/local"  # 或者 /opt/homebrew (M1 Mac)
        if os.path.exists("/opt/homebrew"):
            homebrew_prefix = "/opt/homebrew"
        
        env["PKG_CONFIG_PATH"] = f"{homebrew_prefix}/lib/pkgconfig"
        env["CMAKE_PREFIX_PATH"] = homebrew_prefix
        env["CPPFLAGS"] = f"-I{homebrew_prefix}/include"
        env["LDFLAGS"] = f"-L{homebrew_prefix}/lib"
    
    # 尝试编译安装
    success, _, stderr = run_command([
        sys.executable, "-m", "pip", "install", 
        "--no-binary", "sentencepiece", "sentencepiece"
    ], timeout=600)
    
    if success:
        print("✓ 编译安装sentencepiece成功")
        return True
    else:
        print(f"✗ 编译安装失败: {stderr[:200]}...")
        return False

def fix_sentencepiece_method_4():
    """方法4: 从源码手动编译"""
    print("\n方法4: 从源码手动编译sentencepiece")
    
    original_dir = os.getcwd()
    
    try:
        # 克隆源码
        print("克隆sentencepiece源码...")
        success, _, _ = run_command([
            "git", "clone", "https://github.com/google/sentencepiece.git"
        ])
        
        if not success:
            print("克隆失败")
            return False
        
        os.chdir("sentencepiece")
        
        # 创建build目录
        os.makedirs("build", exist_ok=True)
        os.chdir("build")
        
        # cmake配置
        print("配置cmake...")
        success, _, stderr = run_command([
            "cmake", "..", "-DSPM_ENABLE_SHARED=OFF"
        ])
        
        if not success:
            print(f"cmake配置失败: {stderr[:200]}...")
            return False
        
        # 编译
        print("编译...")
        success, _, stderr = run_command(["make", "-j4"])
        
        if not success:
            print(f"编译失败: {stderr[:200]}...")
            return False
        
        # 安装
        print("安装...")
        success, _, stderr = run_command(["sudo", "make", "install"])
        
        if success:
            print("✓ 源码编译安装成功")
            
            # 现在安装Python绑定
            os.chdir("../python")
            success, _, stderr = run_command([
                sys.executable, "setup.py", "build"
            ])
            
            if success:
                success, _, stderr = run_command([
                    sys.executable, "setup.py", "install"
                ])
                
                if success:
                    print("✓ Python绑定安装成功")
                    return True
        
        return False
        
    except Exception as e:
        print(f"源码编译出错: {e}")
        return False
    finally:
        os.chdir(original_dir)

def test_sentencepiece():
    """测试sentencepiece安装"""
    print("\n测试sentencepiece...")
    
    try:
        import sentencepiece as spm
        print("✓ sentencepiece导入成功")
        
        # 简单测试
        sp = spm.SentencePieceProcessor()
        print("✓ SentencePieceProcessor创建成功")
        return True
        
    except ImportError as e:
        print(f"✗ sentencepiece导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ sentencepiece测试失败: {e}")
        return False

def install_pyannote_after_fix():
    """修复sentencepiece后安装pyannote.audio"""
    print("\n" + "="*60)
    print("安装pyannote.audio")
    print("="*60)
    
    success, stdout, stderr = run_command([
        sys.executable, "-m", "pip", "install", "pyannote.audio"
    ], timeout=600)
    
    if success:
        print("✓ pyannote.audio安装成功")
        
        # 测试导入
        try:
            from pyannote.audio import Pipeline
            print("✓ pyannote.audio导入成功")
            return True
        except ImportError as e:
            print(f"✗ pyannote.audio导入失败: {e}")
            return False
    else:
        print(f"✗ pyannote.audio安装失败: {stderr[:300]}...")
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("解决sentencepiece编译问题并安装pyannote.audio")
    print("=" * 70)
    
    # 1. 安装编译工具
    install_build_tools()
    
    # 2. 尝试多种方法修复sentencepiece
    methods = [
        ("预编译wheel", fix_sentencepiece_method_1),
        ("conda安装", fix_sentencepiece_method_2),
        ("环境变量编译", fix_sentencepiece_method_3),
        ("源码编译", fix_sentencepiece_method_4),
    ]
    
    sentencepiece_success = False
    
    for method_name, method_func in methods:
        print(f"\n尝试 {method_name}...")
        
        try:
            if method_func():
                print(f"✓ {method_name} 成功!")
                if test_sentencepiece():
                    sentencepiece_success = True
                    break
        except Exception as e:
            print(f"✗ {method_name} 出现异常: {e}")
        
        print(f"✗ {method_name} 失败，尝试下一个方法...")
    
    if sentencepiece_success:
        print("\n🎉 sentencepiece安装成功!")
        
        # 3. 安装pyannote.audio
        if install_pyannote_after_fix():
            print("\n🎉 pyannote.audio安装成功!")
            print("现在可以使用说话人分离功能了!")
        else:
            print("\n⚠️ pyannote.audio安装失败，但sentencepiece已修复")
    else:
        print("\n⚠️ sentencepiece安装失败，所有方法都尝试过了")
        print("建议:")
        print("1. 检查Xcode命令行工具是否正确安装")
        print("2. 检查Homebrew是否正常工作")
        print("3. 尝试重启终端后再次运行")

if __name__ == "__main__":
    main()

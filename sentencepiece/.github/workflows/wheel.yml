name: Build Wheels

on:
  push:
    branches: [ master ]
    tags:
      - 'v*'
  pull_request:
    branches: [ master ]

permissions:
  contents: read

jobs:
  build_wheels:
    outputs:
      digests-linux: ${{ steps.hash-linux.outputs.digests }}
      digests-macos: ${{ steps.hash-macos.outputs.digests }}
      digests-windows: ${{ steps.hash-windows.outputs.digests }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macOS-11]
    runs-on: ${{ matrix.os }}
    name: Build wheels on ${{ matrix.os }}

    permissions:
      contents: write # svenstaro/upload-release-action 

    steps:
      - uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
      - uses: actions/setup-python@39cd14951b08e74b54015e9e001cdefcf80e669f # v5.1.1
        with:
          python-version: "3.x"

      - name: Set up QEMU
        if: runner.os == 'Linux'
        uses: docker/setup-qemu-action@49b3bc8e6bdd4a60e6116a5414239cba5943d3cf # v3.2.0
        with:
          platforms: arm64

      - name: Build for Windows
        if: runner.os == 'Windows'
        run: |
          cmake -A Win32 -B ${{github.workspace}}/build_win32 -DSPM_ENABLE_SHARED=OFF -DCMAKE_INSTALL_PREFIX=${{github.workspace}}/build/root_win32
          cmake --build ${{github.workspace}}/build_win32 --config Release --target install --parallel 8
          cmake -A x64   -B ${{github.workspace}}/build_amd64 -DSPM_ENABLE_SHARED=OFF -DCMAKE_INSTALL_PREFIX=${{github.workspace}}/build/root_amd64
          cmake --build ${{github.workspace}}/build_amd64 --config Release --target install --parallel 8
          cmake -A arm64 -B ${{github.workspace}}/build_arm64 -DSPM_ENABLE_SHARED=OFF -DCMAKE_INSTALL_PREFIX=${{github.workspace}}/build/root_arm64
          cmake --build ${{github.workspace}}/build_arm64 --config Release --target install --parallel 8

      - name: Build for Mac
        if: runner.os == 'macOS'
        run: |
          cmake -B ${{github.workspace}}/build -DSPM_ENABLE_SHARED=OFF -DCMAKE_INSTALL_PREFIX=${{github.workspace}}/build/root
          cmake --build ${{github.workspace}}/build --config Release --target install --parallel 8
        env:
          CMAKE_OSX_ARCHITECTURES: arm64;x86_64

      - name: Install cibuildwheel
        working-directory: ${{github.workspace}}/python
        run: |
          python -m pip install --require-hashes --no-dependencies -r ../.github/workflows/requirements/base.txt
          python -m pip install --require-hashes --no-dependencies -r ../.github/workflows/requirements/cibuildwheel.txt

      - name: Build wheels
        working-directory: ${{github.workspace}}/python
        run: python -m cibuildwheel --output-dir wheelhouse
        env:
          CIBW_ARCHS_LINUX: auto aarch64
          CIBW_ARCHS_MACOS: x86_64 universal2 arm64
          CIBW_ARCHS_WINDOWS: auto ARM64
          CIBW_SKIP: "pp* *-musllinux_*"
          CIBW_BUILD_VERBOSITY: 1

      - name: Build sdist archive
        working-directory: ${{github.workspace}}/python
        run: sh build_sdist.sh

      - name: Fetch sdist archive
        uses: tj-actions/glob@2944188f585a0ec102a6a82d9eeb3aed69785393 # v22.0.1
        id: sdist
        with:
          files: ./python/dist/*.tar.gz

      - name: Build wheel from sdist
        run: python -m pip wheel "${{ steps.sdist.outputs.paths }}" --verbose

      - name: Copy sdist
        working-directory: ${{github.workspace}}/python
        if: runner.os == 'macOS'
        run: cp -f dist/*.tar.gz wheelhouse/

      - name: Upload artifact
        uses: actions/upload-artifact@v3
        with:
          name: artifacts
          path: |
            ./python/wheelhouse/*.whl
            ./python/wheelhouse/*.tar.gz

      - name: Upload wheel release
        if: startsWith(github.ref, 'refs/tags/')
        uses: svenstaro/upload-release-action@04733e069f2d7f7f0b4aebc4fbdbce8613b03ccd # v2.9.0
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          file: ./python/wheelhouse/*
          tag: ${{ github.ref }}
          overwrite: true
          prerelease: true
          file_glob: true

      - name: Generate SLSA subjects - Macos
        id: hash-macos
        if: runner.os == 'macOS'
        run: echo "digests=$(shasum -a 256 ./python/wheelhouse/* | base64)" >> $GITHUB_OUTPUT

      - name: Generate SLSA subjects - Linux
        id: hash-linux
        if: runner.os == 'Linux'
        run: echo "digests=$(sha256sum ./python/wheelhouse/* | base64 -w0)" >> $GITHUB_OUTPUT

      - name: Generate SLSA subjects - Windows
        id: hash-windows
        if: runner.os == 'Windows'
        run: echo "digests=$(sha256sum ./python/wheelhouse/* | base64 -w0)" >> $GITHUB_OUTPUT

  gather-disgests:
    needs: [build_wheels]
    outputs:
      digests: ${{ steps.hash.outputs.digests }}
    runs-on: ubuntu-latest
    steps:
      - name: Merge results
        id: hash
        env:
          LINUX_DIGESTS: "${{ needs.build_wheels.outputs.digests-linux }}"
          MACOS_DIGESTS: "${{ needs.build_wheels.outputs.digests-macos }}"
          WINDOWS_DIGESTS: "${{ needs.build_wheels.outputs.digests-windows }}"
        run: |
          set -euo pipefail
          echo "$LINUX_DIGESTS" | base64 -d > checksums.txt
          echo "$MACOS_DIGESTS" | base64 -d >> checksums.txt
          echo "$WINDOWS_DIGESTS" | base64 -d >> checksums.txt
          echo "digests=$(cat checksums.txt | base64 -w0)" >> $GITHUB_OUTPUT

  provenance:
    if: startsWith(github.ref, 'refs/tags/')
    needs: [build_wheels, gather-disgests]
    permissions:
      actions: read   # To read the workflow path.
      id-token: write # To sign the provenance.
      contents: write # To add assets to a release.
    uses: slsa-framework/slsa-github-generator/.github/workflows/generator_generic_slsa3.yml@v2.0.0
    with:
      base64-subjects: "${{ needs.gather-disgests.outputs.digests }}"
      upload-assets: true # Optional: Upload to a new release

import re

@tool
def generate_json(text='', preId_list=None):
    if preId_list is None:
        preId_list = []
    
    text = text.replace('，', ',')
    # 定义中断关键词列表
    interrupt_keywords = ['正在通话中', '留言', '超时', '机主', '无人接听',
                          '转达', '来电秘书', '爱莫能助', '助理', '感谢', '呼叫', '喂，你好，呃，你有什么事？', '不方便接电话', '总共需要还多少钱？', '逾期的话利息是多少来着', '请问您还有什么要补充的', '所需还款金额数是多少', '得还多少钱呀？', '请问逾期利息是多少呢？', '其他需要补充', '智能']
    # 非本人关键词列表
    not_self_keywords = ['不是', '不认', '错', '上面干活','出去','手机']

    result = {}
    
    # 检查文本是否包含任何中断关键词
    for keyword in interrupt_keywords:
        if re.search(keyword, text):
            result = {'id': 'FAQ-026', 'flow': 'B1'}
            preId_list.append(result['id'])
            return result

    # 检查是否包含非本人关键词
    for keyword in not_self_keywords:
        if re.search(keyword, text):
            result = {'id': 'A02', 'flow': 'B2'}
            preId_list.append(result['id'])
            return result

    # 默认返回
    result = {'id': 'A01', 'flow': 'B1'}
    preId_list.append(result['id'])
    return result

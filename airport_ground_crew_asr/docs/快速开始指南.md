# 机场地勤ASR系统 - 快速开始指南

## 系统概述

本系统是一个专门针对机场地勤人员的语音识别(ASR)系统，能够：
- 🎯 **自动识别主要说话人**（录音设备主人，即地勤人员）
- 🔇 **过滤其他人对话**（乘客等非目标说话人）
- 🎙️ **高质量语音转录**（使用Whisper Large-v2模型）
- 📊 **提供结构化结果**（JSON格式，包含时间戳）

## 环境要求

- **操作系统**: macOS/Linux (已在macOS上测试)
- **Python版本**: 3.13+
- **内存**: 8GB+ (用于Large-v2模型)
- **存储**: 5GB+ (模型文件约3GB)
- **网络**: 首次运行需要下载模型

## 快速安装 (5分钟)

### 步骤1: 创建虚拟环境
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
source venv/bin/activate  # macOS/Linux
# 或 venv\Scripts\activate  # Windows
```

### 步骤2: 解决Python 3.13兼容性问题
```bash
# 修复aifc模块问题
python fix_aifc.py
```

### 步骤3: 安装依赖包
```bash
# 安装所有必要的依赖
python alternative_speaker_diarization.py
```

### 步骤4: 下载Whisper Large-v2模型
```bash
# 下载高质量ASR模型 (约3GB)
python download_large_model.py
# 选择选项1: Large-v2 (推荐)
```

### 步骤5: 运行完整系统
```bash
# 处理测试音频
python integrated_asr_system.py
```

## 使用自己的音频文件

### 支持的音频格式
- WAV (推荐)
- MP3
- FLAC
- M4A

### 修改音频文件路径
编辑 `integrated_asr_system.py` 文件，修改第323行：
```python
# 将这行
audio_file = "机场地勤音频.WAV"

# 改为你的音频文件
audio_file = "your_audio_file.wav"
```

### 运行处理
```bash
python integrated_asr_system.py
```

## 输出结果说明

### 控制台输出
```
处理结果:
音频文件: your_audio.wav
总时长: 56.29 秒
主要说话人时长: 31.20 秒
主要说话人占比: 55.4%
处理时间: 67.07 秒

说话人分离:
检测到说话人数: 2
主要说话人: 1

转录结果:
有效片段数: 7
总词数: 18

主要说话人详细转录:
------------------------------------------------------------
 1. [   0.0s -    8.8s] 蓝色位置是可以选择的几个人蓝色 两个人就从俩位置
 2. [  17.6s -   20.0s] 没了没了就直接飞机过去
 ...
```

### JSON结果文件
系统会自动生成详细的JSON结果文件：
- `integrated_asr_result_YYYYMMDD_HHMMSS.json`

JSON结构：
```json
{
  "system_info": {
    "version": "整合ASR系统 v1.0",
    "whisper_model": "large-v2",
    "processing_time": 67.07
  },
  "audio_info": {
    "file_path": "your_audio.wav",
    "total_duration": 56.29,
    "main_speaker_duration": 31.20,
    "main_speaker_percentage": 55.4
  },
  "speaker_diarization": {
    "num_speakers": 2,
    "main_speaker": 1,
    "speaker_durations": {...}
  },
  "transcription": {
    "segments": [...],
    "full_text": "完整转录文本"
  }
}
```

## 常见问题解决

### Q1: 模型下载失败
**解决方案**:
```bash
# 检查网络连接
ping huggingface.co

# 重新下载
python download_large_model.py
```

### Q2: aifc模块错误
**错误**: `No module named 'aifc'`
**解决方案**:
```bash
python fix_aifc.py
```

### Q3: 内存不足
**错误**: `CUDA out of memory` 或系统卡顿
**解决方案**:
- 确保有8GB+可用内存
- 关闭其他大型应用程序
- 使用较小的模型（修改为"base"）

### Q4: 音频格式不支持
**错误**: 音频加载失败
**解决方案**:
```bash
# 安装额外的音频处理库
pip install pydub ffmpeg-python

# 或转换音频格式
ffmpeg -i input.mp3 output.wav
```

### Q5: 转录结果为空
**可能原因**:
- 音频质量太差
- 没有检测到语音
- 说话人分离失败

**解决方案**:
```bash
# 使用分析工具检查
python asr_analysis.py
```

## 性能优化建议

### 1. 硬件优化
- **CPU**: 多核处理器，推荐8核+
- **内存**: 16GB+推荐
- **存储**: SSD硬盘提升模型加载速度

### 2. 软件优化
```python
# 在 integrated_asr_system.py 中调整参数
class IntegratedASRSystem:
    def __init__(self, whisper_model="large-v2"):
        # 对于较慢的机器，可以使用较小的模型
        # whisper_model="base"  # 更快但准确性稍低
```

### 3. 批量处理
```python
# 处理多个文件
audio_files = ["file1.wav", "file2.wav", "file3.wav"]
for audio_file in audio_files:
    result = asr_system.process_audio(audio_file)
    # 保存结果
```

## 高级用法

### 1. 自定义说话人分离参数
编辑 `speaker_diarization_system.py`:
```python
def __init__(self):
    self.window_size = 1.6  # 调整窗口大小
    self.step_size = 0.8    # 调整步长
```

### 2. 自定义ASR参数
编辑 `improved_asr.py`:
```python
def transcribe_with_high_quality(self, audio_path):
    segments, info = self.model.transcribe(
        audio_path,
        beam_size=5,        # 调整束搜索宽度
        temperature=[0.0],  # 使用确定性解码
        # 其他参数...
    )
```

### 3. 集成到其他系统
```python
from integrated_asr_system import IntegratedASRSystem

# 创建系统实例
asr_system = IntegratedASRSystem()
asr_system.initialize()

# 处理音频
result = asr_system.process_audio("your_audio.wav")

# 获取主要说话人文本
main_speaker_text = result['transcription']['full_text']
```

## 技术支持

### 查看详细文档
- 📖 `机场地勤ASR系统开发总结.md` - 完整技术文档
- 📋 `项目文件清单.md` - 所有文件说明

### 调试工具
```bash
# 测试说话人分离
python speaker_diarization_system.py

# 测试ASR
python improved_asr.py

# 分析结果
python asr_analysis.py
```

### 日志文件
系统运行时会生成日志文件：
- `asr_system_YYYYMMDD.log`

## 下一步

1. **验证结果**: 对比原音频验证转录准确性
2. **调整参数**: 根据实际需求优化系统参数
3. **扩展功能**: 添加关键信息提取、情感分析等功能
4. **部署生产**: 参考文档中的Docker部署配置

---

🎉 **恭喜！您已经成功部署了机场地勤ASR系统！**

如有问题，请查看详细技术文档或检查日志文件。

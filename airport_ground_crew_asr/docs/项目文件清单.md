# 机场地勤ASR系统 - 项目文件清单

## 核心系统文件

### 1. `integrated_asr_system.py` - 完整整合系统 ⭐
**功能**: 结合说话人分离和ASR的完整系统
**关键特性**:
- 自动说话人分离
- 主要说话人识别
- 高质量Whisper Large-v2转录
- 结构化JSON输出

### 2. `speaker_diarization_system.py` - 说话人分离核心 ⭐
**功能**: 基于resemblyzer的说话人分离
**关键特性**:
- 声纹特征提取
- 无监督聚类
- 主要说话人自动识别
- 时间段精确定位

### 3. `improved_asr.py` - 优化ASR系统
**功能**: 使用Large-v2模型的高质量ASR
**关键特性**:
- 多温度采样
- VAD语音活动检测
- 词级时间戳
- 质量过滤

### 4. `audio_loader.py` - 通用音频加载器 ⭐
**功能**: 多层级备用音频处理方案
**关键特性**:
- 支持多种音频格式
- 自动重采样
- 兼容性处理
- 错误恢复

## 环境配置和修复文件

### 5. `fix_aifc.py` - Python 3.13兼容性修复 ⭐
**功能**: 解决aifc模块缺失问题
**关键特性**:
- 手动实现aifc模块
- 多种音频库安装
- 兼容性测试

### 6. `download_large_model.py` - 模型下载工具
**功能**: 下载Whisper Large-v2模型
**关键特性**:
- 断点续传下载
- 进度条显示
- 网络错误处理
- 模型验证

### 7. `alternative_speaker_diarization.py` - 替代库安装
**功能**: 安装说话人分离相关库
**关键特性**:
- 批量包安装
- 依赖检查
- 导入测试

### 8. `install_pyannote_advanced.py` - 高级安装方案
**功能**: 多方案安装pyannote.audio
**关键特性**:
- 多种安装方法
- 错误处理
- 备用方案

## 分析和测试工具

### 9. `asr_analysis.py` - ASR结果分析工具
**功能**: 详细分析ASR转录结果
**关键特性**:
- 质量统计
- 说话人分析
- 可视化报告

### 10. `test_filter.py` - 过滤器测试
**功能**: 测试地勤语音过滤器
**关键特性**:
- 规则测试
- 准确性评估
- 示例验证

### 11. `ground_crew_filter.py` - 地勤语音过滤器
**功能**: 基于文本内容的地勤语音识别
**关键特性**:
- 关键词匹配
- 角色识别
- 置信度计算

### 12. `test_large_model.py` - 大模型测试
**功能**: 测试Large-v2模型加载
**关键特性**:
- 多种加载方式
- 错误诊断
- 性能测试

## 早期版本和实验文件

### 13. `real_asr.py` - 基础ASR系统
**功能**: 最初的ASR实现
**关键特性**:
- 基础Whisper集成
- 简单地勤过滤
- 分块处理

### 14. `install_pyannote.py` - 基础安装脚本
**功能**: 基础pyannote.audio安装
**关键特性**:
- 基本依赖安装
- 导入测试

## 输出结果文件

### 15. 转录结果文件
- `real_asr_result_*.json` - 基础ASR结果
- `improved_asr_result_*.json` - 优化ASR结果
- `speaker_diarization_result_*.json` - 说话人分离结果
- `integrated_asr_result_*.json` - 完整系统结果 ⭐

### 16. 日志文件
- `asr_system_*.log` - 系统运行日志
- 各种调试和错误日志

## 配置和文档文件

### 17. `机场地勤ASR系统开发总结.md` - 完整技术文档 ⭐
**内容**:
- 完整开发过程
- 所有核心代码
- 技术难点解决方案
- 性能优化技术
- 部署配置

### 18. `项目文件清单.md` - 本文件
**内容**:
- 所有文件说明
- 功能描述
- 重要性标记

## 测试音频文件

### 19. `机场地勤音频.WAV` - 测试音频
**规格**:
- 时长: 56.29秒
- 格式: WAV
- 内容: 机场地勤与乘客对话

## 推荐使用顺序

### 新用户快速开始:
1. 阅读 `机场地勤ASR系统开发总结.md`
2. 运行 `fix_aifc.py` 修复兼容性
3. 运行 `alternative_speaker_diarization.py` 安装依赖
4. 运行 `download_large_model.py` 下载模型
5. 运行 `integrated_asr_system.py` 完整系统

### 开发者深入研究:
1. 研究 `speaker_diarization_system.py` 了解说话人分离
2. 研究 `improved_asr.py` 了解ASR优化
3. 研究 `audio_loader.py` 了解音频处理
4. 使用 `asr_analysis.py` 分析结果

### 问题排查:
1. 使用 `test_large_model.py` 测试模型
2. 检查各种日志文件
3. 运行单独的组件测试

## 文件重要性标记

⭐ **核心文件** - 系统正常运行必需
🔧 **工具文件** - 辅助开发和调试
📊 **分析文件** - 结果分析和评估
📝 **文档文件** - 说明和指导
🧪 **测试文件** - 验证和测试

## 系统架构图

```
机场地勤ASR系统
├── 音频输入 (机场地勤音频.WAV)
├── 音频预处理 (audio_loader.py)
├── 说话人分离 (speaker_diarization_system.py)
│   ├── 声纹特征提取 (resemblyzer)
│   ├── 聚类分析 (scikit-learn)
│   └── 主要说话人识别
├── ASR转录 (improved_asr.py)
│   ├── Whisper Large-v2模型
│   ├── 优化参数配置
│   └── 质量过滤
├── 结果整合 (integrated_asr_system.py)
└── 输出结果 (JSON格式)
```

## 依赖关系

```
integrated_asr_system.py
├── speaker_diarization_system.py
│   ├── audio_loader.py
│   ├── resemblyzer
│   └── scikit-learn
├── improved_asr.py
│   └── faster-whisper (Large-v2)
└── 各种工具和修复文件
```

这个项目文件清单提供了完整的文件概览，帮助理解每个文件的作用和相互关系。

2025-05-27 14:20:43,985 - INFO - 切换到工作目录: /Users/<USER>/Desktop/python-project/airport_ground_crew_asr/src
2025-05-27 14:20:43,985 - INFO - ✅ 测试音频文件 - 文件存在: ../data/机场地勤音频.WAV
2025-05-27 14:20:43,985 - INFO - 
==================================================
2025-05-27 14:20:43,985 - INFO - 步骤1: 修复Python 3.13兼容性
2025-05-27 14:20:43,985 - INFO - ==================================================
2025-05-27 14:20:43,985 - INFO - 开始执行: 修复aifc模块兼容性
2025-05-27 14:20:43,985 - INFO - 命令: python fix_aifc.py
2025-05-27 14:20:45,426 - INFO - ✅ 修复aifc模块兼容性 - 成功
2025-05-27 14:20:45,426 - INFO - 输出: ======================================================================
修复Python 3.13中的aifc模块问题
======================================================================
============================================================
解决aifc模块问题
============================================================
方法1: 安装aifc替代包...
✗ aifc-compat安装失败: ERROR: Could not find a version that satisfies the requirement aifc-compat (from versions: none)
ERROR: No matching distribution found for aifc-compat


方法2: 手动创建aifc模块...
创建aifc模块: /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages/aifc.py
✓ 手动创建aifc模块成功

测试aifc模块...
✓ aifc模块导入成功
✓ aifc模块基本功能正常

测试音频处理库...
✓ librosa
✓ soundfile
✗ pydub
✓ wave
✓ audioread
创建通用音频加载器: audio_loader.py

======================================================================
修复总结
======================================================================
✓ aifc模块问题已解决
✓ 可用音频库: librosa, soundfile, wave, audioread
✓ 创建了通用音频加载器

🎉 音频处理问题已解决！
现在可以继续使用说话人分离系统了

2025-05-27 14:20:45,426 - INFO - 
==================================================
2025-05-27 14:20:45,426 - INFO - 步骤2: 安装说话人分离依赖
2025-05-27 14:20:45,426 - INFO - ==================================================
2025-05-27 14:20:45,426 - INFO - 开始执行: 安装说话人分离依赖
2025-05-27 14:20:45,427 - INFO - 命令: python alternative_speaker_diarization.py
2025-05-27 14:20:59,765 - INFO - ✅ 安装说话人分离依赖 - 成功
2025-05-27 14:20:59,766 - INFO - 输出: ======================================================================
替代说话人分离方案
======================================================================
======================================================================
安装替代的说话人分离工具
======================================================================

安装 resemblyzer...
✓ resemblyzer

安装 librosa...
✓ librosa

安装 soundfile...
✓ soundfile

安装 scipy...
✓ scipy

安装 scikit-learn...
✓ scikit-learn

安装 matplotlib...
✓ matplotlib

安装 seaborn...
✓ seaborn

安装 torch...
✓ torch

安装 torchaudio...
✓ torchaudio

安装 webrtcvad...
✓ webrtcvad

安装 pydub...
✓ pydub

安装 noisereduce...
✓ noisereduce

成功安装 12/12 个包

✓ 替代包安装成功

============================================================
测试替代库导入
============================================================
✓ resemblyzer 可用
✓ librosa 0.10.2.post1
✓ torch 2.6.0
✓ scikit-learn 1.6.1
✓ webrtcvad 可用

============================================================
尝试安装pyannote.audio (跳过problematic依赖)
============================================================
安装 pyannote.core...
✓ pyannote.core
安装 pyannote.database...
✓ pyannote.database
安装 pyannote.metrics...
✓ pyannote.metrics
安装 pyannote.pipeline...
✓ pyannote.pipeline

尝试安装pyannote.audio (忽略依赖错误)...
✓ pyannote.audio (无依赖)
✓ pyannote.core 导入成功

======================================================================
安装总结
======================================================================
✓ 可用工具: resemblyzer, librosa, torch, sklearn, webrtcvad

🎉 推荐使用 resemblyzer 进行说话人分离
resemblyzer 是一个简单易用的说话人分离库

🎉 可以使用 librosa + sklearn 实现基础说话人分离
基于音频特征的聚类方法

🎉 pyannote.core 可用，可以使用基础功能

现在可以创建说话人分离系统了！

2025-05-27 14:20:59,767 - INFO - 
==================================================
2025-05-27 14:20:59,767 - INFO - 步骤3: 下载Whisper Large-v2模型
2025-05-27 14:20:59,767 - INFO - ==================================================
2025-05-27 14:20:59,767 - INFO - 开始执行: 下载Whisper Large-v2模型
2025-05-27 14:20:59,767 - INFO - 命令: python download_large_model.py
2025-05-27 14:21:09,952 - INFO - ✅ 下载Whisper Large-v2模型 - 成功
2025-05-27 14:21:09,952 - INFO - 输出: ======================================================================
Whisper Large模型下载工具
======================================================================
✗ 网络连接失败: HTTPSConnectionPool(host='huggingface.co', port=443): Read timed out. (read timeout=10)
请检查网络连接和翻墙设置

2025-05-27 14:21:09,952 - INFO - 
==================================================
2025-05-27 14:21:09,952 - INFO - 步骤4: 测试大模型加载
2025-05-27 14:21:09,952 - INFO - ==================================================
2025-05-27 14:21:09,952 - INFO - 开始执行: 测试大模型加载
2025-05-27 14:21:09,952 - INFO - 命令: python test_large_model.py
2025-05-27 14:22:38,161 - INFO - ✅ 测试大模型加载 - 成功
2025-05-27 14:22:38,163 - INFO - 输出: ============================================================
Whisper Large-v2 模型测试
============================================================
正在测试Large-v2模型加载...
方法1: 直接加载large-v2
✗ 方法1失败: Unable to open file 'model.bin' in model '/Users/<USER>/.cache/huggingface/hub/models--Systran--faster-whisper-large-v2/snapshots/f0fe81560cb8b68660e564f55dd99207059c092e'

方法2: 使用本地路径
✓ 方法2成功: 本地路径加载成功

测试完成

2025-05-27 14:22:38,163 - INFO - 
==================================================
2025-05-27 14:22:38,163 - INFO - 步骤5: 运行完整ASR系统
2025-05-27 14:22:38,163 - INFO - ==================================================
2025-05-27 14:22:38,163 - INFO - 更新音频文件路径...
2025-05-27 14:22:38,165 - INFO - 音频文件路径更新成功
2025-05-27 14:22:38,165 - INFO - 开始执行: 运行完整ASR系统
2025-05-27 14:22:38,165 - INFO - 命令: python integrated_asr_system.py
2025-05-27 14:24:14,637 - INFO - ✅ 运行完整ASR系统 - 成功
2025-05-27 14:24:14,639 - INFO - 输出: ================================================================================
整合的机场地勤ASR系统
说话人分离 + 主要说话人ASR
================================================================================
Loaded the voice encoder model on cpu in 0.01 seconds.

处理结果:
音频文件: ../data/机场地勤音频.WAV
总时长: 56.29 秒
主要说话人时长: 31.20 秒
主要说话人占比: 55.4%
处理时间: 88.60 秒

说话人分离:
检测到说话人数: 2
主要说话人: 1

转录结果:
有效片段数: 7
总词数: 10

主要说话人详细转录:
------------------------------------------------------------
 1. [   0.0s -    8.8s] 蓝色位置是可以选择的几个人蓝色 两个人就从俩位置
 2. [  11.2s -   12.8s] 看俩位子了
 3. [  15.2s -   16.8s] 就這樣了
 4. [  17.6s -   20.0s] 没了没了就直接飞机过去
 5. [  24.8s -   32.0s] 能中间的对啊你手机订单给我看一下两位在哪买的李丽华
 6. [  36.0s -   39.2s] 你拿手机给他看对 没打出来 那个身份证给我
 7. [  44.8s -   51.2s] 我們今天就說到這裡希望大家喜歡今天的影片
------------------------------------------------------------

主要说话人完整文本:
============================================================
蓝色位置是可以选择的几个人蓝色 两个人就从俩位置 看俩位子了 就這樣了 没了没了就直接飞机过去 能中间的对啊你手机订单给我看一下两位在哪买的李丽华 你拿手机给他看对 没打出来 那个身份证给我 我們今天就說到這裡希望大家喜歡今天的影片
============================================================

详细结果已保存到: integrated_asr_result_20250527_142413.json

2025-05-27 14:24:14,642 - INFO - 🎉 ASR系统运行成功！
2025-05-27 14:24:14,643 - INFO - 结果文件已移动到: ../results/integrated_asr_result_20250527_142413.json

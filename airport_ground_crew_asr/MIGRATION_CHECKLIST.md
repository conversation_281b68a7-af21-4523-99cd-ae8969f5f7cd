# 机场地勤ASR系统 - GPU机器迁移清单

## 📦 打包完成状态

✅ **项目已成功打包**
- 压缩包: `airport_ground_crew_asr_20250527_222724.tar.gz` (6.0MB)
- 备用格式: `airport_ground_crew_asr_20250527_222724.zip` (6.0MB)

## 📋 迁移清单

### 🔄 需要传输的文件

#### 必需文件 (已打包)
- [x] `airport_ground_crew_asr_*.tar.gz` - 主项目包
- [x] 源代码文件
- [x] 部署文档
- [x] 安装脚本
- [x] 测试音频 (53秒)

#### 大文件 (需单独传输)
- [ ] `完整音频.mp3` (8小时原始音频)
- [ ] `完整音频_1小时.wav` (已截取的1小时音频)

### 🚀 GPU机器部署步骤

#### 1. 环境准备
```bash
# 检查Python版本 (需要3.8+)
python3 --version

# 检查GPU环境
nvidia-smi
```

#### 2. 项目部署
```bash
# 解压项目
tar -xzf airport_ground_crew_asr_*.tar.gz
cd airport_ground_crew_asr_*

# 运行安装脚本
chmod +x install.sh
./install.sh

# 激活环境
source venv/bin/activate
```

#### 3. GPU优化
```bash
# 自动检测并配置GPU
python optimize_gpu.py

# 手动检查GPU状态
python -c "import torch; print(f'CUDA: {torch.cuda.is_available()}')"
```

#### 4. 模型下载配置
```bash
# 设置镜像 (中国用户)
export HF_ENDPOINT=https://hf-mirror.com

# 或添加到环境变量
echo 'export HF_ENDPOINT=https://hf-mirror.com' >> ~/.bashrc
source ~/.bashrc
```

#### 5. 测试运行
```bash
cd src

# 测试小音频文件
python ground_crew_asr_main.py ../data/机场地勤音频.WAV --fast-mode

# 预期结果: 处理时间应显著减少 (GPU加速)
```

#### 6. 长音频处理
```bash
# 如果有完整音频文件，先截取1小时
python extract_audio_segment.py ../data/完整音频.mp3 --duration 3600

# 处理1小时音频
python ground_crew_asr_main.py ../data/完整音频_segment.wav --fast-mode
```

### 📊 性能基准

#### 当前CPU性能 (MacBook Pro)
- **测试音频**: 53.20秒
- **标准模式**: 220.73秒 (4.15x实时)
- **快速模式**: 159.30秒 (3.0x实时)

#### GPU预期性能
- **目标**: 50-80秒处理53秒音频 (1.0-1.5x实时)
- **1小时音频**: 预期1-2小时完成 (vs CPU的6-8小时)

### 🔧 故障排除

#### 常见问题
1. **模型下载失败**
   ```bash
   export HF_ENDPOINT=https://hf-mirror.com
   # 或使用代理
   ```

2. **GPU内存不足**
   ```bash
   # 检查显存使用
   nvidia-smi
   # 如果不足，使用CPU模式
   ```

3. **依赖包冲突**
   ```bash
   # 重新创建环境
   rm -rf venv
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

### 📝 验证清单

#### 部署验证
- [ ] Python环境正常 (3.8+)
- [ ] GPU检测成功
- [ ] 依赖包安装完成
- [ ] 模型下载成功
- [ ] 测试音频处理正常

#### 性能验证
- [ ] GPU加速生效
- [ ] 处理速度提升明显
- [ ] 输出格式正确
- [ ] 说话人分离准确

### 🎯 下一步开发任务

1. **性能优化**
   - 测试GPU加速效果
   - 调整batch_size和参数
   - 优化内存使用

2. **长音频处理**
   - 处理1小时音频
   - 评估质量和速度
   - 优化大文件处理流程

3. **批处理功能**
   - 实现多文件并行处理
   - 添加进度显示
   - 错误恢复机制

### 📞 AI助手对接信息

#### 项目状态
- **当前版本**: 使用Whisper Large-v2模型
- **优化状态**: 快速模式已调优
- **回退功能**: 说话人合并、时间段格式 (用户不满意)

#### 用户偏好
- 中文回复
- 专注地勤与乘客对话
- 优先处理速度
- 简洁的输出格式

#### 技术栈
- faster-whisper (Large-v2)
- pyannote-audio (说话人分离)
- PyTorch (GPU加速)
- ffmpeg (音频处理)

---

**迁移完成后，请使用此清单验证部署状态，并继续开发工作。**

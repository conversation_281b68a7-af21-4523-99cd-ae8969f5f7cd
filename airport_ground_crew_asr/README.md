# 机场地勤 ASR 系统

专门针对机场地勤人员的语音识别系统，能够从包含多人对话的音频中自动识别并转录主要说话人（录音设备主人，即地勤人员）的语音内容。

## 🎯 核心功能

- **说话人分离**: 基于声纹特征自动识别不同说话人
- **主要说话人识别**: 自动识别录音设备主人（地勤人员）
- **高质量 ASR**: 支持 Whisper Large-v3 (最新)、Large-v2 和阿里 SenseVoice 模型进行中文语音识别
- **多模型选择**: 可选择 Whisper Large-v3 (最佳准确性) 或 SenseVoice (速度优先)
- **结构化输出**: 提供 JSON 格式的详细转录结果

## 📁 项目结构

```
airport_ground_crew_asr/
├── src/                    # 源代码
│   ├── integrated_asr_system.py      # 完整整合系统
│   ├── speaker_diarization_system.py # 说话人分离核心
│   ├── improved_asr.py               # 优化ASR系统
│   ├── audio_loader.py               # 通用音频加载器
│   ├── fix_aifc.py                   # Python 3.13兼容性修复
│   ├── download_large_model.py       # 模型下载工具
│   └── ...                          # 其他工具文件
├── data/                   # 音频数据
│   └── 机场地勤音频.WAV
├── docs/                   # 文档
│   ├── 机场地勤ASR系统开发总结.md
│   ├── 项目文件清单.md
│   └── 快速开始指南.md
├── results/                # 输出结果
├── tools/                  # 工具脚本
├── requirements.txt        # 依赖包
└── README.md              # 项目说明
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 环境修复（Python 3.13）

```bash
cd src
python fix_aifc.py
```

### 3. 安装说话人分离依赖

```bash
python alternative_speaker_diarization.py
```

### 4. 下载 Whisper Large-v3 模型

```bash
# 下载最新的Large-v3模型 (推荐)
python download_large_v3_model.py

# 或下载Large-v2模型 (备用)
python download_large_model.py
```

### 5. 运行完整系统

**选择 1: 使用 Whisper Large-v3 (最佳准确性)**

```bash
python integrated_asr_system.py
```

**选择 2: 使用 SenseVoice (速度优先)**

```bash
# 安装SenseVoice依赖
python install_sensevoice.py

# 运行SenseVoice系统
python integrated_sensevoice_asr_system.py
```

**一键运行脚本**

```bash
# Whisper系统
python run_asr_system.py

# SenseVoice系统
python run_sensevoice_system.py
```

## 📊 系统性能

### Whisper Large-v3 模式 (推荐)

- **处理速度**: 约 1.5 倍实时速度 (比 v2 快 25%)
- **识别准确性**: 比 Large-v2 提升 10-20%，业界最佳
- **模型大小**: 约 3GB
- **内存占用**: 优化后略低于 v2
- **特色功能**: 更精确时间戳、更强噪音抗性

### Whisper Large-v2 模式 (备用)

- **处理速度**: 约 1.2 倍实时速度
- **识别准确性**: 优秀的中文识别准确性
- **模型大小**: 约 3GB
- **内存占用**: 较高

### SenseVoice 模式

- **处理速度**: 约 15 倍实时速度 (70ms 处理 10 秒音频)
- **识别准确性**: 中文和粤语识别超越 Whisper
- **模型大小**: 约 1GB
- **内存占用**: 较低
- **额外功能**: 情感识别、音频事件检测

### 通用特性

- **说话人分离**: 基于声纹特征，无需预设规则
- **支持格式**: WAV、MP3、FLAC 等多种音频格式

## 🔧 技术架构

- **说话人分离**: resemblyzer + scikit-learn
- **语音识别**: faster-whisper (Large-v2 模型)
- **音频处理**: librosa + soundfile + pydub
- **开发环境**: Python 3.13, 支持 macOS/Linux

## 📖 详细文档

- [开发总结](docs/机场地勤ASR系统开发总结.md) - 完整技术文档
- [文件清单](docs/项目文件清单.md) - 所有文件说明
- [快速指南](docs/快速开始指南.md) - 5 分钟快速上手

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📄 许可证

MIT License

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用pyannote-audio的说话人分离系统
这是业界最好的说话人分离工具
"""

import os
import sys
import logging
import warnings
import numpy as np
from datetime import datetime

# 抑制警告
warnings.filterwarnings("ignore")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from pyannote.audio import Pipeline
    from pyannote.core import Annotation, Segment
    PYANNOTE_AVAILABLE = True
    logger.info("pyannote-audio可用")
except ImportError as e:
    PYANNOTE_AVAILABLE = False
    logger.error(f"pyannote-audio不可用: {e}")

class PyAnnoteSpeakerDiarization:
    """使用pyannote-audio的说话人分离系统"""

    def __init__(self):
        self.pipeline = None
        logger.info("初始化pyannote说话人分离系统")

    def initialize(self):
        """初始化pyannote pipeline"""
        try:
            if not PYANNOTE_AVAILABLE:
                raise ImportError("pyannote-audio不可用")

            logger.info("构建自定义说话人分离pipeline...")

            # 使用pyannote的组件构建自定义pipeline
            from pyannote.audio.pipelines import SpeakerDiarization
            from pyannote.audio import Model

            # 创建自定义pipeline，不依赖预训练模型
            pipeline = SpeakerDiarization(
                segmentation="pyannote/segmentation-3.0",
                embedding="pyannote/embedding",
                clustering="AgglomerativeClustering"
            )

            self.pipeline = pipeline
            logger.info("✅ 自定义pyannote pipeline构建成功")
            return True

        except Exception as e:
            logger.error(f"自定义pipeline构建失败: {e}")

            # 尝试使用更简单的方法
            try:
                logger.info("尝试使用基础组件...")
                from pyannote.audio.pipelines.speaker_verification import PretrainedSpeakerEmbedding
                from pyannote.audio.pipelines.clustering import AgglomerativeClustering

                # 使用基础组件
                self.embedding = PretrainedSpeakerEmbedding("speechbrain/spkrec-ecapa-voxceleb")
                self.clustering = AgglomerativeClustering()

                logger.info("✅ 基础组件加载成功")
                return True

            except Exception as e2:
                logger.error(f"基础组件也失败: {e2}")
                return False

    def process_audio(self, audio_path):
        """处理音频文件进行说话人分离"""
        try:
            logger.info(f"开始处理音频文件: {audio_path}")
            start_time = datetime.now()

            if not self.pipeline:
                raise RuntimeError("Pipeline未初始化")

            # 使用pyannote进行说话人分离
            logger.info("执行说话人分离...")
            diarization = self.pipeline(audio_path)

            # 分析结果
            speakers = list(diarization.labels())
            num_speakers = len(speakers)

            logger.info(f"检测到 {num_speakers} 个说话人: {speakers}")

            # 计算每个说话人的总时长
            speaker_durations = {}
            speaker_segments = {}

            for speaker in speakers:
                speaker_durations[speaker] = 0.0
                speaker_segments[speaker] = []

            for segment, _, speaker in diarization.itertracks(yield_label=True):
                duration = segment.end - segment.start
                speaker_durations[speaker] += duration
                speaker_segments[speaker].append((segment.start, segment.end))

            # 确定主要说话人（说话时间最长的）
            main_speaker = max(speaker_durations.items(), key=lambda x: x[1])[0]
            main_speaker_duration = speaker_durations[main_speaker]
            main_speaker_segments = speaker_segments[main_speaker]

            # 获取音频总时长
            audio_duration = max([segment.end for segment, _, _ in diarization.itertracks(yield_label=True)])

            # 合并相近的片段
            merged_segments = self._merge_close_segments(main_speaker_segments, gap_threshold=1.0)

            processing_time = (datetime.now() - start_time).total_seconds()

            result = {
                'success': True,
                'num_speakers': num_speakers,
                'speakers': speakers,
                'main_speaker': main_speaker,
                'speaker_durations': speaker_durations,
                'main_speaker_segments': merged_segments,
                'audio_duration': audio_duration,
                'processing_time': processing_time,
                'raw_diarization': diarization
            }

            logger.info(f"说话人分离完成，用时 {processing_time:.2f} 秒")
            logger.info(f"主要说话人: {main_speaker}, 时长: {main_speaker_duration:.2f}秒")
            logger.info(f"主要说话人片段数: {len(merged_segments)}")

            return result

        except Exception as e:
            logger.error(f"说话人分离失败: {e}")
            return None

    def _merge_close_segments(self, segments, gap_threshold=1.0):
        """合并相近的片段"""
        if not segments:
            return []

        # 按开始时间排序
        sorted_segments = sorted(segments, key=lambda x: x[0])
        merged = [sorted_segments[0]]

        for current_start, current_end in sorted_segments[1:]:
            last_start, last_end = merged[-1]

            # 如果当前片段与上一个片段的间隔小于阈值，则合并
            if current_start - last_end <= gap_threshold:
                merged[-1] = (last_start, max(last_end, current_end))
            else:
                merged.append((current_start, current_end))

        return merged

def test_pyannote_system():
    """测试pyannote说话人分离系统"""
    print("=" * 60)
    print("测试pyannote说话人分离系统")
    print("=" * 60)

    # 检查音频文件
    audio_file = "../data/机场地勤音频.WAV"
    if not os.path.exists(audio_file):
        print(f"错误: 音频文件不存在 - {audio_file}")
        return

    # 创建系统
    system = PyAnnoteSpeakerDiarization()

    # 初始化
    if not system.initialize():
        print("系统初始化失败")
        return

    # 处理音频
    result = system.process_audio(audio_file)

    if result:
        print(f"\n处理结果:")
        print(f"检测到说话人数: {result['num_speakers']}")
        print(f"说话人列表: {result['speakers']}")
        print(f"主要说话人: {result['main_speaker']}")
        print(f"音频总时长: {result['audio_duration']:.2f}秒")
        print(f"处理时间: {result['processing_time']:.2f}秒")

        print(f"\n说话人时长统计:")
        for speaker, duration in result['speaker_durations'].items():
            percentage = (duration / result['audio_duration']) * 100
            marker = " (主要说话人)" if speaker == result['main_speaker'] else ""
            print(f"  {speaker}: {duration:.2f}秒 ({percentage:.1f}%){marker}")

        print(f"\n主要说话人片段:")
        for i, (start, end) in enumerate(result['main_speaker_segments'], 1):
            print(f"  片段{i}: {start:.1f}s - {end:.1f}s (时长: {end-start:.1f}s)")

    else:
        print("处理失败")

if __name__ == "__main__":
    test_pyannote_system()

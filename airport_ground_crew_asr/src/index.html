<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>机场地勤ASR系统架构流程图 - WhisperX & pyannote.audio</title>
    <style>
      body {
        margin: 0;
        padding: 20px;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .header {
        text-align: center;
        margin-bottom: 30px;
        color: #2c3e50;
      }

      .header h1 {
        font-size: 28px;
        font-weight: 700;
        margin: 0 0 10px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .header p {
        font-size: 16px;
        color: #7f8c8d;
        margin: 0;
        font-weight: 400;
      }

      .svg-container {
        background: #ffffff;
        border-radius: 16px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        padding: 20px;
        max-width: 100%;
        overflow-x: auto;
      }

      svg {
        display: block;
        max-width: 100%;
        height: auto;
      }

      /* SVG样式定义 */
      .io-box {
        fill: #e8f6f8;
        stroke: #26a69a;
        stroke-width: 2.5;
        rx: 12;
        ry: 12;
        filter: url(#drop-shadow);
        transition: all 0.3s ease;
      }

      .io-box:hover {
        fill: #b2dfdb;
        stroke: #00695c;
      }

      .process-box {
        fill: #e3f2fd;
        stroke: #1976d2;
        stroke-width: 2.5;
        rx: 10;
        ry: 10;
        filter: url(#drop-shadow);
        transition: all 0.3s ease;
      }

      .process-box:hover {
        fill: #bbdefb;
        stroke: #0d47a1;
      }

      .data-box {
        fill: #fff8e1;
        stroke: #f57c00;
        stroke-width: 2.5;
        rx: 10;
        ry: 10;
        filter: url(#drop-shadow);
        transition: all 0.3s ease;
      }

      .data-box:hover {
        fill: #ffecb3;
        stroke: #e65100;
      }

      .core-tech-box {
        fill: #f3e5f5;
        stroke: #7b1fa2;
        stroke-width: 3;
        rx: 12;
        ry: 12;
        filter: url(#drop-shadow);
      }

      .title-text {
        font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
        font-size: 18px;
        font-weight: 700;
        fill: #1a237e;
        text-anchor: middle;
      }

      .main-text {
        font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
        font-size: 14px;
        font-weight: 600;
        fill: #263238;
        text-anchor: middle;
      }

      .sub-text {
        font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
        font-size: 11px;
        font-weight: 400;
        fill: #546e7a;
        text-anchor: middle;
      }

      .tech-highlight {
        font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
        font-size: 16px;
        font-weight: 700;
        fill: #1565c0;
        text-anchor: middle;
      }

      .arrow {
        stroke: #607d8b;
        stroke-width: 3;
        fill: none;
        marker-end: url(#arrowhead);
        filter: url(#glow);
      }

      .flow-label {
        font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
        font-size: 10px;
        font-weight: 500;
        fill: #37474f;
        text-anchor: middle;
      }

      .container-border {
        stroke: #90a4ae;
        stroke-width: 3;
        stroke-dasharray: 12 6;
        fill: none;
        rx: 20;
        ry: 20;
        opacity: 0.8;
      }

      .container-title {
        font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
        font-size: 20px;
        font-weight: 700;
        fill: #37474f;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>机场地勤ASR系统架构流程图</h1>
      <p>基于 WhisperX 和 pyannote.audio 的智能语音识别解决方案</p>
    </div>

    <div class="svg-container">
      <svg width="1200" height="800" viewBox="0 0 1200 800">
        <defs>
          <!-- 箭头标记定义 -->
          <marker
            id="arrowhead"
            markerWidth="12"
            markerHeight="8"
            refX="10"
            refY="4"
            orient="auto"
            markerUnits="strokeWidth"
          >
            <polygon points="0 0, 12 4, 0 8" fill="#607d8b" />
          </marker>

          <!-- 阴影滤镜 -->
          <filter id="drop-shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="4" />
            <feOffset dx="3" dy="3" result="offsetblur" />
            <feFlood flood-color="rgba(0,0,0,0.15)" />
            <feComposite in2="offsetblur" operator="in" />
            <feMerge>
              <feMergeNode />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>

          <!-- 发光效果 -->
          <filter id="glow">
            <feGaussianBlur stdDeviation="2" result="coloredBlur" />
            <feMerge>
              <feMergeNode in="coloredBlur" />
              <feMergeNode in="SourceGraphic" />
            </feMerge>
          </filter>

          <!-- 渐变定义 -->
          <linearGradient id="headerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color: #1976d2; stop-opacity: 1" />
            <stop offset="100%" style="stop-color: #42a5f5; stop-opacity: 1" />
          </linearGradient>
        </defs>

        <!-- 系统标题区域 -->
        <rect
          x="30"
          y="30"
          width="1140"
          height="60"
          fill="url(#headerGradient)"
          rx="15"
          ry="15"
          filter="url(#drop-shadow)"
        />
        <text x="600" y="65" class="title-text" fill="white">
          机场地勤ASR系统 - WhisperX集成架构
        </text>

        <!-- 主容器边框 -->
        <rect
          x="50"
          y="120"
          width="1100"
          height="650"
          class="container-border"
        />
        <text x="80" y="145" class="container-title">WhisperX 主控流程</text>

        <!-- 阶段标识 -->
        <text x="150" y="180" class="main-text" fill="#1976d2">输入阶段</text>
        <text x="400" y="180" class="main-text" fill="#1976d2">预处理阶段</text>
        <text x="700" y="180" class="main-text" fill="#1976d2">识别阶段</text>
        <text x="1000" y="180" class="main-text" fill="#1976d2">输出阶段</text>

        <!-- 1. 音频输入 -->
        <rect x="80" y="220" width="140" height="90" class="io-box" />
        <text x="150" y="250" class="main-text">🎤 音频输入</text>
        <text x="150" y="270" class="sub-text">机场地勤通信录音</text>
        <text x="150" y="285" class="sub-text">支持: MP3, WAV, M4A</text>
        <text x="150" y="300" class="sub-text">多说话人环境</text>

        <!-- 2. pyannote.audio 核心模块 -->
        <rect x="280" y="200" width="200" height="130" class="core-tech-box" />
        <text x="380" y="230" class="tech-highlight">pyannote.audio</text>
        <text x="380" y="250" class="main-text">🔍 语音活动检测 (VAD)</text>
        <text x="380" y="270" class="main-text">👥 说话人分离</text>
        <text x="380" y="285" class="sub-text">Speaker Diarization</text>
        <text x="380" y="300" class="sub-text">时间戳精确定位</text>
        <text x="380" y="315" class="sub-text">多说话人识别</text>

        <!-- 3. 分离结果数据 -->
        <rect x="530" y="200" width="180" height="110" class="data-box" />
        <text x="620" y="230" class="main-text">📊 分离结果</text>
        <text x="620" y="250" class="main-text">语音片段列表</text>
        <text x="620" y="270" class="sub-text">开始时间 | 结束时间</text>
        <text x="620" y="285" class="sub-text">说话人ID标识</text>
        <text x="620" y="300" class="sub-text">音频片段索引</text>

        <!-- 4. 语音片段提取处理 -->
        <rect x="530" y="380" width="180" height="90" class="process-box" />
        <text x="620" y="410" class="main-text">✂️ 片段提取</text>
        <text x="620" y="430" class="sub-text">根据时间戳切割</text>
        <text x="620" y="445" class="sub-text">保持音频质量</text>
        <text x="620" y="460" class="sub-text">批量处理优化</text>

        <!-- 5. OpenAI Whisper ASR -->
        <rect x="760" y="350" width="180" height="130" class="core-tech-box" />
        <text x="850" y="380" class="tech-highlight">OpenAI Whisper</text>
        <text x="850" y="400" class="main-text">🎯 语音识别引擎</text>
        <text x="850" y="420" class="sub-text">模型: large-v3</text>
        <text x="850" y="435" class="sub-text">高精度转录</text>
        <text x="850" y="450" class="sub-text">多语言支持</text>
        <text x="850" y="465" class="sub-text">并行处理</text>

        <!-- 6. ASR转录结果 -->
        <rect x="760" y="520" width="180" height="90" class="data-box" />
        <text x="850" y="550" class="main-text">📝 转录文本</text>
        <text x="850" y="570" class="sub-text">带时间戳文本</text>
        <text x="850" y="585" class="sub-text">置信度评分</text>
        <text x="850" y="600" class="sub-text">语言检测结果</text>

        <!-- 7. 结果整合处理 -->
        <rect x="530" y="550" width="180" height="110" class="process-box" />
        <text x="620" y="580" class="main-text">🔄 结果整合</text>
        <text x="620" y="600" class="sub-text">合并说话人信息</text>
        <text x="620" y="615" class="sub-text">时间轴对齐</text>
        <text x="620" y="630" class="sub-text">格式标准化</text>
        <text x="620" y="645" class="sub-text">质量验证</text>

        <!-- 8. 最终输出 -->
        <rect x="280" y="580" width="180" height="110" class="io-box" />
        <text x="370" y="610" class="main-text">📤 输出文件</text>
        <text x="370" y="630" class="sub-text">SRT字幕文件</text>
        <text x="370" y="645" class="sub-text">JSON结构化数据</text>
        <text x="370" y="660" class="sub-text">VTT网页字幕</text>
        <text x="370" y="675" class="sub-text">CSV分析报表</text>

        <!-- 9. 应用场景说明 -->
        <rect x="980" y="220" width="160" height="130" class="io-box" />
        <text x="1060" y="250" class="main-text">🛫 应用场景</text>
        <text x="1060" y="270" class="sub-text">地勤通信监控</text>
        <text x="1060" y="285" class="sub-text">安全事件分析</text>
        <text x="1060" y="300" class="sub-text">培训质量评估</text>
        <text x="1060" y="315" class="sub-text">工作流程优化</text>
        <text x="1060" y="330" class="sub-text">合规性检查</text>

        <!-- 数据流箭头连接 -->

        <!-- 1. 音频输入 -> pyannote.audio -->
        <path d="M220 265 L280 265" class="arrow" />
        <text x="250" y="255" class="flow-label">原始音频流</text>

        <!-- 2. pyannote.audio -> 分离结果 -->
        <path d="M480 265 L530 255" class="arrow" />
        <text x="505" y="250" class="flow-label">分离数据</text>

        <!-- 3. 分离结果 -> 片段提取 -->
        <path d="M620 310 L620 380" class="arrow" />
        <text x="640" y="345" class="flow-label">时间戳信息</text>

        <!-- 4. 音频输入 -> 片段提取 (原始音频) -->
        <path d="M150 310 Q150 350 530 425" class="arrow" />
        <text x="340" y="370" class="flow-label">原始音频数据</text>

        <!-- 5. 片段提取 -> Whisper ASR -->
        <path d="M710 425 L760 415" class="arrow" />
        <text x="735" y="410" class="flow-label">音频片段</text>

        <!-- 6. Whisper ASR -> 转录结果 -->
        <path d="M850 480 L850 520" class="arrow" />
        <text x="870" y="500" class="flow-label">文本输出</text>

        <!-- 7. 分离结果 -> 结果整合 (说话人信息) -->
        <path d="M620 310 Q620 450 620 550" class="arrow" />
        <text x="640" y="430" class="flow-label">说话人标签</text>

        <!-- 8. 转录结果 -> 结果整合 -->
        <path d="M760 565 L710 605" class="arrow" />
        <text x="735" y="575" class="flow-label">转录文本</text>

        <!-- 9. 结果整合 -> 输出文件 -->
        <path d="M530 635 L460 635" class="arrow" />
        <text x="495" y="625" class="flow-label">最终结果</text>

        <!-- 10. 输出文件 -> 应用场景 -->
        <path d="M460 635 Q800 635 1060 350" class="arrow" />
        <text x="760" y="625" class="flow-label">实际应用</text>

        <!-- 技术特性标注 -->
        <rect
          x="80"
          y="720"
          width="1060"
          height="50"
          fill="#f8f9fa"
          stroke="#dee2e6"
          stroke-width="1"
          rx="8"
          ry="8"
        />
        <text x="600" y="740" class="main-text" fill="#495057">
          核心技术特性
        </text>
        <text x="200" y="755" class="sub-text">• 实时处理能力</text>
        <text x="350" y="755" class="sub-text">• 高精度识别</text>
        <text x="480" y="755" class="sub-text">• 多说话人分离</text>
        <text x="620" y="755" class="sub-text">• 标准化输出</text>
        <text x="750" y="755" class="sub-text">• 模块化架构</text>
        <text x="880" y="755" class="sub-text">• 可扩展设计</text>
        <text x="1000" y="755" class="sub-text">• 企业级应用</text>
      </svg>
    </div>
  </body>
</html>

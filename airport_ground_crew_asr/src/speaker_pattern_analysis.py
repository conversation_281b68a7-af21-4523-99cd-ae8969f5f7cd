#!/usr/bin/env python3
"""
说话人模式分析工具
基于说话时长、频率等模式识别地勤和乘客
"""

import json
import argparse
from pathlib import Path
import matplotlib.pyplot as plt
import numpy as np

def analyze_speaking_patterns(json_file):
    """分析说话模式"""
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    transcriptions = data.get('transcription', {}).get('all_speaker_transcriptions', {})
    speaker_durations = data.get('speaker_diarization', {}).get('speaker_durations', {})
    all_segments = data.get('speaker_diarization', {}).get('all_segments', [])
    
    # 分析每个说话人的模式
    speaker_analysis = {}
    
    for speaker_id in transcriptions.keys():
        # 获取该说话人的所有片段
        speaker_segments = [seg for seg in all_segments if seg['speaker'] == speaker_id]
        
        # 计算统计信息
        total_duration = speaker_durations.get(speaker_id, 0)
        segment_count = len(speaker_segments)
        avg_segment_length = total_duration / segment_count if segment_count > 0 else 0
        
        # 计算说话频率（每分钟说话次数）
        total_audio_duration = data.get('audio_info', {}).get('total_duration', 1)
        speaking_frequency = (segment_count / total_audio_duration) * 60  # 每分钟次数
        
        # 文本分析
        text = transcriptions.get(speaker_id, '')
        word_count = len(text.split()) if text else 0
        avg_words_per_segment = word_count / segment_count if segment_count > 0 else 0
        
        speaker_analysis[speaker_id] = {
            'total_duration': total_duration,
            'segment_count': segment_count,
            'avg_segment_length': avg_segment_length,
            'speaking_frequency': speaking_frequency,
            'word_count': word_count,
            'avg_words_per_segment': avg_words_per_segment,
            'text': text
        }
    
    return speaker_analysis

def classify_by_patterns(speaker_analysis):
    """基于模式分类说话人"""
    # 获取所有说话人的统计数据
    durations = [info['total_duration'] for info in speaker_analysis.values()]
    frequencies = [info['speaking_frequency'] for info in speaker_analysis.values()]
    avg_lengths = [info['avg_segment_length'] for info in speaker_analysis.values()]
    
    # 计算阈值（基于中位数）
    duration_threshold = np.median(durations)
    frequency_threshold = np.median(frequencies)
    length_threshold = np.median(avg_lengths)
    
    classifications = {}
    
    for speaker_id, info in speaker_analysis.items():
        score = 0
        reasons = []
        
        # 规则1: 说话时间长的可能是地勤（提供更多信息）
        if info['total_duration'] > duration_threshold:
            score += 1
            reasons.append("说话时间较长")
        
        # 规则2: 说话频率高的可能是地勤（主动交流）
        if info['speaking_frequency'] > frequency_threshold:
            score += 1
            reasons.append("说话频率较高")
        
        # 规则3: 平均片段长度长的可能是地勤（详细解释）
        if info['avg_segment_length'] > length_threshold:
            score += 1
            reasons.append("单次说话较长")
        
        # 规则4: 词汇量大的可能是地勤（专业术语）
        if info['word_count'] > np.median([i['word_count'] for i in speaker_analysis.values()]):
            score += 1
            reasons.append("词汇量较大")
        
        # 分类决策
        if score >= 3:
            role = 'ground_crew'
            confidence = score / 4
        elif score <= 1:
            role = 'passenger'
            confidence = (4 - score) / 4
        else:
            role = 'uncertain'
            confidence = 0.5
        
        classifications[speaker_id] = {
            'role': role,
            'confidence': confidence,
            'score': score,
            'reasons': reasons,
            **info
        }
    
    return classifications

def generate_pattern_report(classifications, output_file):
    """生成模式分析报告"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("机场地勤ASR系统 - 说话模式分析报告\n")
        f.write("=" * 60 + "\n\n")
        
        # 按角色分组
        ground_crew = {k: v for k, v in classifications.items() if v['role'] == 'ground_crew'}
        passengers = {k: v for k, v in classifications.items() if v['role'] == 'passenger'}
        uncertain = {k: v for k, v in classifications.items() if v['role'] == 'uncertain'}
        
        # 地勤人员
        if ground_crew:
            f.write("🧑‍💼 地勤人员 (基于说话模式识别):\n")
            f.write("-" * 40 + "\n")
            for speaker_id, info in ground_crew.items():
                f.write(f"{speaker_id} (置信度: {info['confidence']:.2f}):\n")
                f.write(f"  说话时长: {info['total_duration']:.1f}秒\n")
                f.write(f"  说话次数: {info['segment_count']}次\n")
                f.write(f"  平均片段长度: {info['avg_segment_length']:.1f}秒\n")
                f.write(f"  说话频率: {info['speaking_frequency']:.1f}次/分钟\n")
                f.write(f"  识别依据: {', '.join(info['reasons'])}\n")
                f.write(f"  内容: {info['text'][:100]}{'...' if len(info['text']) > 100 else ''}\n\n")
        
        # 乘客
        if passengers:
            f.write("🧳 乘客 (基于说话模式识别):\n")
            f.write("-" * 40 + "\n")
            for speaker_id, info in passengers.items():
                f.write(f"{speaker_id} (置信度: {info['confidence']:.2f}):\n")
                f.write(f"  说话时长: {info['total_duration']:.1f}秒\n")
                f.write(f"  说话次数: {info['segment_count']}次\n")
                f.write(f"  平均片段长度: {info['avg_segment_length']:.1f}秒\n")
                f.write(f"  说话频率: {info['speaking_frequency']:.1f}次/分钟\n")
                f.write(f"  内容: {info['text'][:100]}{'...' if len(info['text']) > 100 else ''}\n\n")
        
        # 不确定
        if uncertain:
            f.write("❓ 角色不确定:\n")
            f.write("-" * 40 + "\n")
            for speaker_id, info in uncertain.items():
                f.write(f"{speaker_id}:\n")
                f.write(f"  说话时长: {info['total_duration']:.1f}秒\n")
                f.write(f"  内容: {info['text'][:100]}{'...' if len(info['text']) > 100 else ''}\n\n")

def main():
    parser = argparse.ArgumentParser(description='说话人模式分析工具')
    parser.add_argument('json_file', help='ASR结果JSON文件路径')
    parser.add_argument('--output', '-o', help='输出报告文件路径')
    
    args = parser.parse_args()
    
    if not Path(args.json_file).exists():
        print(f"错误: 文件不存在 - {args.json_file}")
        return 1
    
    print("分析说话模式...")
    speaker_analysis = analyze_speaking_patterns(args.json_file)
    
    print("基于模式分类说话人...")
    classifications = classify_by_patterns(speaker_analysis)
    
    # 显示结果
    print("\n📊 说话模式分析结果:")
    print("=" * 50)
    
    for speaker_id, info in classifications.items():
        role_cn = {
            'ground_crew': '地勤人员',
            'passenger': '乘客',
            'uncertain': '不确定'
        }.get(info['role'], '未知')
        
        print(f"{speaker_id}: {role_cn} (置信度: {info['confidence']:.2f})")
        print(f"  时长: {info['total_duration']:.1f}s, 次数: {info['segment_count']}, 频率: {info['speaking_frequency']:.1f}/min")
        if info['reasons']:
            print(f"  依据: {', '.join(info['reasons'])}")
        print()
    
    # 生成报告
    if args.output:
        output_file = args.output
    else:
        json_path = Path(args.json_file)
        output_file = json_path.parent / f"{json_path.stem}_pattern_analysis.txt"
    
    generate_pattern_report(classifications, output_file)
    print(f"✅ 模式分析报告已保存到: {output_file}")

if __name__ == "__main__":
    import sys
    sys.exit(main())

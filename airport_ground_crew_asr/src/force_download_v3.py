#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制下载完整的Whisper Large-v3模型
确保所有必要文件都正确下载
"""

import os
import sys
import logging
import time
import subprocess
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_model_files(model_path):
    """检查模型文件完整性"""
    required_files = [
        'model.bin',
        'config.json',
        'tokenizer.json',
        'vocabulary.json',
        'preprocessor_config.json'
    ]

    missing_files = []
    for file in required_files:
        file_path = os.path.join(model_path, file)
        if not os.path.exists(file_path):
            missing_files.append(file)

    return missing_files

def force_download_large_v3():
    """强制下载完整的Large-v3模型"""
    try:
        print("=" * 70)
        print("强制下载Whisper Large-v3模型")
        print("=" * 70)

        # 设置环境变量优化下载 - 使用国内镜像加速
        os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'  # 使用国内镜像加速
        os.environ['HF_HUB_DISABLE_PROGRESS_BARS'] = '0'     # 显示进度条
        os.environ['HF_HUB_ENABLE_HF_TRANSFER'] = '1'        # 启用快速传输
        os.environ['HF_HUB_DOWNLOAD_TIMEOUT'] = '600'        # 10分钟超时

        logger.info(f"✓ 使用HF镜像加速: {os.environ['HF_ENDPOINT']}")

        # 检查网络连接
        logger.info("检查网络连接...")
        try:
            import requests
            response = requests.get('https://huggingface.co', timeout=10)
            if response.status_code == 200:
                logger.info("✓ 网络连接正常")
            else:
                logger.warning("⚠ 网络连接可能有问题")
        except Exception as e:
            logger.warning(f"⚠ 网络检查失败: {e}")

        # 方法1: 使用huggingface_hub直接下载
        logger.info("方法1: 使用huggingface_hub下载...")
        try:
            from huggingface_hub import snapshot_download

            model_path = snapshot_download(
                repo_id="Systran/faster-whisper-large-v3",
                cache_dir=os.path.expanduser("~/.cache/huggingface/hub"),
                resume_download=True,
                local_files_only=False
            )

            logger.info(f"✓ 模型下载到: {model_path}")

            # 检查文件完整性
            missing_files = check_model_files(model_path)
            if not missing_files:
                logger.info("✓ 所有必要文件下载完成")
                return model_path
            else:
                logger.warning(f"⚠ 缺少文件: {missing_files}")

        except Exception as e:
            logger.error(f"✗ huggingface_hub下载失败: {e}")

        # 方法2: 使用faster-whisper强制下载
        logger.info("方法2: 使用faster-whisper强制下载...")
        try:
            from faster_whisper import WhisperModel

            # 强制重新下载
            logger.info("开始下载Large-v3模型（约3GB）...")
            logger.info("请耐心等待，不要中断...")

            start_time = time.time()

            model = WhisperModel(
                "large-v3",
                device="cpu",
                compute_type="int8",
                download_root=os.path.expanduser("~/.cache/huggingface/hub"),
                local_files_only=False
            )

            download_time = time.time() - start_time
            logger.info(f"✓ 模型下载成功，用时: {download_time:.2f}秒")

            # 验证模型可用性
            logger.info("验证模型可用性...")

            # 简单测试
            test_audio = "../data/机场地勤音频.WAV"
            if os.path.exists(test_audio):
                logger.info("使用测试音频验证...")
                segments, info = model.transcribe(
                    test_audio,
                    language="zh",
                    beam_size=1,
                    best_of=1,
                    temperature=0.0
                )

                # 获取第一个片段
                first_segment = next(segments, None)
                if first_segment:
                    logger.info(f"✓ 模型验证成功")
                    logger.info(f"测试转录: {first_segment.text}")
                    return True
                else:
                    logger.warning("⚠ 模型验证：未获得转录结果")
            else:
                logger.info("未找到测试音频，跳过验证")

            return True

        except Exception as e:
            logger.error(f"✗ faster-whisper下载失败: {e}")

        # 方法3: 使用git lfs下载
        logger.info("方法3: 尝试git lfs下载...")
        try:
            cache_dir = os.path.expanduser("~/.cache/huggingface/hub")
            repo_dir = os.path.join(cache_dir, "models--Systran--faster-whisper-large-v3")

            if os.path.exists(repo_dir):
                os.system(f"rm -rf {repo_dir}")

            # 使用git clone
            cmd = f"cd {cache_dir} && git clone https://huggingface.co/Systran/faster-whisper-large-v3 models--Systran--faster-whisper-large-v3"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                logger.info("✓ git clone成功")

                # 下载LFS文件
                lfs_cmd = f"cd {repo_dir} && git lfs pull"
                lfs_result = subprocess.run(lfs_cmd, shell=True, capture_output=True, text=True)

                if lfs_result.returncode == 0:
                    logger.info("✓ LFS文件下载成功")
                    return repo_dir
                else:
                    logger.error(f"✗ LFS下载失败: {lfs_result.stderr}")
            else:
                logger.error(f"✗ git clone失败: {result.stderr}")

        except Exception as e:
            logger.error(f"✗ git方法失败: {e}")

        return False

    except Exception as e:
        logger.error(f"下载过程出错: {e}")
        return False

def verify_v3_model():
    """验证v3模型是否正确安装"""
    try:
        logger.info("验证Large-v3模型安装...")

        from faster_whisper import WhisperModel

        # 尝试加载模型
        model = WhisperModel("large-v3", device="cpu", compute_type="int8")
        logger.info("✓ Large-v3模型加载成功")

        # 检查模型路径
        model_path = os.path.expanduser("~/.cache/huggingface/hub/models--Systran--faster-whisper-large-v3")
        if os.path.exists(model_path):
            logger.info(f"✓ 模型路径存在: {model_path}")

            # 检查文件
            missing_files = check_model_files(model_path)
            if not missing_files:
                logger.info("✓ 所有必要文件完整")
                return True
            else:
                logger.error(f"✗ 缺少文件: {missing_files}")
                return False
        else:
            logger.error("✗ 模型路径不存在")
            return False

    except Exception as e:
        logger.error(f"✗ 模型验证失败: {e}")
        return False

def main():
    """主函数"""
    print("Whisper Large-v3强制下载工具")
    print("确保下载完整的v3模型文件")

    # 检查当前状态
    if verify_v3_model():
        print("🎉 Large-v3模型已正确安装！")
        return

    print("\n需要下载Large-v3模型...")

    # 强制下载
    success = force_download_large_v3()

    print("\n" + "="*70)
    print("下载结果")
    print("="*70)

    if success and verify_v3_model():
        print("🎉 Large-v3模型下载并验证成功！")
        print("\n可以运行:")
        print("python integrated_asr_system.py")
        print("\n系统将使用真正的Large-v3模型")
    else:
        print("❌ Large-v3模型下载失败")
        print("\n可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 确保有足够磁盘空间 (至少4GB)")
        print("3. 尝试使用VPN")
        print("4. 手动从HuggingFace下载")

if __name__ == "__main__":
    main()

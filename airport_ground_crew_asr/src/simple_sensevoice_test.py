#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的SenseVoice测试脚本
用于验证SenseVoice是否能正常工作
"""

import os
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_sensevoice_simple():
    """简单测试SenseVoice"""
    try:
        print("=" * 60)
        print("简单SenseVoice测试")
        print("=" * 60)
        
        # 1. 测试导入
        print("1. 测试导入...")
        from funasr import AutoModel
        print("✓ funasr导入成功")
        
        # 2. 尝试最简单的模型加载
        print("2. 加载SenseVoice模型...")
        
        # 使用最简单的配置
        try:
            model = AutoModel(model="iic/SenseVoiceSmall")
            print("✓ SenseVoice模型加载成功")
        except Exception as e:
            print(f"✗ 标准方式加载失败: {e}")
            
            # 尝试备用方式
            try:
                model = AutoModel(
                    model="iic/SenseVoiceSmall",
                    disable_update=True,
                    trust_remote_code=False
                )
                print("✓ SenseVoice模型加载成功 (备用方式)")
            except Exception as e2:
                print(f"✗ 备用方式也失败: {e2}")
                return False
        
        # 3. 测试转录功能
        print("3. 测试转录功能...")
        
        # 检查测试音频文件
        audio_file = "../data/机场地勤音频.WAV"
        if not os.path.exists(audio_file):
            print(f"✗ 测试音频文件不存在: {audio_file}")
            return False
        
        print(f"使用音频文件: {audio_file}")
        
        # 进行转录
        try:
            res = model.generate(
                input=audio_file,
                language="auto",
                use_itn=True,
            )
            
            if res and len(res) > 0:
                print("✓ 转录成功")
                print(f"原始结果: {res[0]}")
                
                # 尝试后处理
                try:
                    from funasr.utils.postprocess_utils import rich_transcription_postprocess
                    text = rich_transcription_postprocess(res[0]["text"])
                    print(f"处理后文本: {text}")
                except Exception as e:
                    print(f"后处理失败，使用原始文本: {res[0].get('text', '')}")
                    text = res[0].get('text', '')
                
                print(f"\n最终转录结果: {text}")
                return True
            else:
                print("✗ 转录结果为空")
                return False
                
        except Exception as e:
            print(f"✗ 转录失败: {e}")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_whisper_comparison():
    """对比测试Whisper"""
    try:
        print("\n" + "=" * 60)
        print("对比测试Whisper")
        print("=" * 60)
        
        from faster_whisper import WhisperModel
        
        # 加载Whisper模型
        model = WhisperModel("large-v2", device="cpu", compute_type="int8")
        print("✓ Whisper Large-v2模型加载成功")
        
        # 转录同一个音频文件
        audio_file = "../data/机场地勤音频.WAV"
        if os.path.exists(audio_file):
            segments, info = model.transcribe(audio_file, language="zh")
            
            print(f"Whisper转录结果:")
            full_text = ""
            for segment in segments:
                print(f"[{segment.start:.1f}s - {segment.end:.1f}s] {segment.text}")
                full_text += segment.text + " "
            
            print(f"\nWhisper完整文本: {full_text.strip()}")
            return True
        else:
            print("✗ 音频文件不存在，跳过Whisper测试")
            return False
            
    except Exception as e:
        print(f"✗ Whisper测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始SenseVoice简单测试...")
    
    sensevoice_success = test_sensevoice_simple()
    whisper_success = test_whisper_comparison()
    
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    if sensevoice_success:
        print("🎉 SenseVoice测试成功！")
    else:
        print("❌ SenseVoice测试失败")
    
    if whisper_success:
        print("✓ Whisper对比测试成功")
    else:
        print("⚠ Whisper对比测试失败")
    
    if sensevoice_success:
        print("\n下一步可以运行完整的整合系统:")
        print("python integrated_sensevoice_asr_system.py")
    else:
        print("\n建议继续使用Whisper系统:")
        print("python integrated_asr_system.py")

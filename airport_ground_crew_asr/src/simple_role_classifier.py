#!/usr/bin/env python3
"""
简单角色分类器
基于说话时长判断：说话最多的是地勤，其他是乘客
"""

import json
import argparse
from pathlib import Path

def classify_by_duration(json_file):
    """基于说话时长进行简单分类"""
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    transcriptions = data.get('transcription', {}).get('all_speaker_transcriptions', {})
    speaker_durations = data.get('speaker_diarization', {}).get('speaker_durations', {})
    total_duration = data.get('audio_info', {}).get('total_duration', 0)
    
    # 按说话时长排序
    sorted_speakers = sorted(speaker_durations.items(), key=lambda x: x[1], reverse=True)
    
    print("=" * 60)
    print("基于说话时长的角色分类")
    print("=" * 60)
    print(f"音频总时长: {total_duration:.1f}秒")
    print()
    
    classifications = {}
    
    for i, (speaker_id, duration) in enumerate(sorted_speakers):
        percentage = (duration / total_duration) * 100 if total_duration > 0 else 0
        text = transcriptions.get(speaker_id, '')
        
        # 简单规则：说话时间最长的是地勤，其他是乘客
        if i == 0:  # 说话最多的
            role = '地勤人员'
            role_en = 'ground_crew'
        else:
            role = '乘客'
            role_en = 'passenger'
        
        classifications[speaker_id] = {
            'role': role,
            'role_en': role_en,
            'duration': duration,
            'percentage': percentage,
            'rank': i + 1,
            'text': text
        }
        
        print(f"排名 {i+1}: {speaker_id} -> {role}")
        print(f"  说话时长: {duration:.1f}秒 ({percentage:.1f}%)")
        print(f"  内容: {text[:80]}{'...' if len(text) > 80 else ''}")
        print()
    
    return classifications

def generate_simple_output(classifications, output_file):
    """生成简化的分类输出"""
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("机场地勤ASR系统 - 简单角色分类 (基于说话时长)\n")
        f.write("=" * 60 + "\n\n")
        
        # 按角色分组
        ground_crew = []
        passengers = []
        
        for speaker_id, info in classifications.items():
            if info['role_en'] == 'ground_crew':
                ground_crew.append((speaker_id, info))
            else:
                passengers.append((speaker_id, info))
        
        # 地勤人员 (说话最多的)
        f.write("🧑‍💼 地勤人员 (说话时间最长):\n")
        f.write("-" * 40 + "\n")
        for speaker_id, info in ground_crew:
            f.write(f"{speaker_id} (时长: {info['duration']:.1f}s, 占比: {info['percentage']:.1f}%):\n")
            f.write(f"  {info['text']}\n\n")
        
        # 乘客 (其他人)
        f.write("🧳 乘客 (其他说话人):\n")
        f.write("-" * 40 + "\n")
        for speaker_id, info in passengers:
            f.write(f"{speaker_id} (时长: {info['duration']:.1f}s, 占比: {info['percentage']:.1f}%):\n")
            f.write(f"  {info['text']}\n\n")
        
        # 统计信息
        f.write("📊 统计信息:\n")
        f.write("-" * 40 + "\n")
        total_ground_crew_time = sum(info['duration'] for _, info in ground_crew)
        total_passenger_time = sum(info['duration'] for _, info in passengers)
        
        f.write(f"地勤说话时间: {total_ground_crew_time:.1f}秒\n")
        f.write(f"乘客说话时间: {total_passenger_time:.1f}秒\n")
        f.write(f"地勤/乘客时间比: {total_ground_crew_time/total_passenger_time:.1f}:1\n")

def main():
    parser = argparse.ArgumentParser(description='简单角色分类器 (基于说话时长)')
    parser.add_argument('json_file', help='ASR结果JSON文件路径')
    parser.add_argument('--output', '-o', help='输出分类结果文件路径')
    
    args = parser.parse_args()
    
    if not Path(args.json_file).exists():
        print(f"错误: 文件不存在 - {args.json_file}")
        return 1
    
    # 分类
    classifications = classify_by_duration(args.json_file)
    
    # 生成输出
    if args.output:
        output_file = args.output
    else:
        json_path = Path(args.json_file)
        output_file = json_path.parent / f"{json_path.stem}_simple_classified.txt"
    
    generate_simple_output(classifications, output_file)
    print(f"✅ 简单分类结果已保存到: {output_file}")
    
    # 显示关键信息
    ground_crew_speakers = [k for k, v in classifications.items() if v['role_en'] == 'ground_crew']
    passenger_speakers = [k for k, v in classifications.items() if v['role_en'] == 'passenger']
    
    print(f"\n📋 分类结果:")
    print(f"地勤人员: {', '.join(ground_crew_speakers)}")
    print(f"乘客: {', '.join(passenger_speakers)}")

if __name__ == "__main__":
    import sys
    sys.exit(main())

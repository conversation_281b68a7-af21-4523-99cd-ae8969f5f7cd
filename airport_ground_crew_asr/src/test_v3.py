#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Whisper Large-v3模型是否可用
"""

def test_v3():
    try:
        print("测试Large-v3模型...")
        
        from faster_whisper import WhisperModel
        
        # 尝试加载模型
        model = WhisperModel("large-v3", device="cpu", compute_type="int8")
        
        print("✅ Large-v3模型加载成功！")
        print("模型可以正常使用")
        
        return True
        
    except Exception as e:
        print(f"❌ Large-v3模型测试失败: {e}")
        return False

if __name__ == "__main__":
    if test_v3():
        print("\n🎉 Large-v3模型已就绪！")
        print("可以运行: python integrated_asr_system.py")
    else:
        print("\n⚠ 建议使用Large-v2模型")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
官方pyannote-audio说话人分离系统
使用HuggingFace Token访问高质量预训练模型
"""

import os
import sys
import logging
import warnings
import numpy as np
from datetime import datetime

# 抑制警告
warnings.filterwarnings("ignore")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# HuggingFace Token
HF_TOKEN = "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"

# 设置环境变量
os.environ['HF_TOKEN'] = HF_TOKEN
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'

try:
    from pyannote.audio import Pipeline
    from pyannote.core import Annotation, Segment
    from huggingface_hub import login

    # 登录HuggingFace
    login(token=HF_TOKEN)

    PYANNOTE_AVAILABLE = True
    logger.info("pyannote-audio可用，HuggingFace已登录")
except ImportError as e:
    PYANNOTE_AVAILABLE = False
    logger.error(f"pyannote-audio不可用: {e}")

class OfficialPyAnnoteDiarization:
    """官方pyannote说话人分离系统"""

    def __init__(self):
        self.pipeline = None
        logger.info("初始化官方pyannote说话人分离系统")

    def initialize(self):
        """初始化官方pyannote pipeline"""
        try:
            if not PYANNOTE_AVAILABLE:
                raise ImportError("pyannote-audio不可用")

            logger.info("加载官方pyannote说话人分离pipeline...")

            # 模型列表，按优先级排序
            models_to_try = [
                "pyannote/speaker-diarization-3.1",
                "pyannote/speaker-diarization-3.0",
                "pyannote/speaker-diarization"
            ]

            for model_name in models_to_try:
                try:
                    logger.info(f"尝试加载模型: {model_name}")

                    # 按优先级尝试不同的认证方式
                    configs_to_try = [
                        {"use_auth_token": True},  # 使用已保存的token
                        {"use_auth_token": HF_TOKEN},  # 直接使用token
                        {"token": HF_TOKEN},  # 新版本API
                        {}  # 无token尝试
                    ]

                    for i, config in enumerate(configs_to_try, 1):
                        try:
                            logger.info(f"  尝试配置 {i}: {config}")
                            self.pipeline = Pipeline.from_pretrained(model_name, **config)
                            if self.pipeline is not None:
                                logger.info(f"✅ {model_name} 加载成功 (配置: {config})")
                                return True
                        except Exception as config_error:
                            logger.warning(f"  配置 {i} 失败: {str(config_error)[:100]}...")
                            continue

                    logger.error(f"❌ {model_name} 所有配置都失败")

                except Exception as model_error:
                    logger.error(f"❌ {model_name} 完全失败: {model_error}")
                    continue

            # 如果所有模型都失败，提供详细的错误信息和解决方案
            logger.error("所有pyannote模型都无法加载")
            logger.error("可能的原因和解决方案:")
            logger.error("1. 网络问题 - 检查网络连接或使用VPN")
            logger.error("2. 模型需要接受用户条件 - 访问 https://hf.co/pyannote/speaker-diarization")
            logger.error("3. Token权限问题 - 检查HuggingFace Token是否有效")
            logger.error("4. 模型被gated - 需要申请访问权限")

            return False

        except Exception as e:
            logger.error(f"初始化过程出错: {e}")
            return False

    def process_audio(self, audio_path):
        """处理音频文件进行说话人分离"""
        try:
            logger.info(f"开始处理音频文件: {audio_path}")
            start_time = datetime.now()

            if not self.pipeline:
                raise RuntimeError("Pipeline未初始化")

            # 使用官方pyannote进行说话人分离
            logger.info("执行官方pyannote说话人分离...")
            diarization = self.pipeline(audio_path)

            # 分析结果
            speakers = list(diarization.labels())
            num_speakers = len(speakers)

            logger.info(f"检测到 {num_speakers} 个说话人: {speakers}")

            # 计算每个说话人的总时长
            speaker_durations = {}
            speaker_segments = {}

            for speaker in speakers:
                speaker_durations[speaker] = 0.0
                speaker_segments[speaker] = []

            for segment, _, speaker in diarization.itertracks(yield_label=True):
                duration = segment.end - segment.start
                speaker_durations[speaker] += duration
                speaker_segments[speaker].append((segment.start, segment.end))

            # 确定主要说话人（说话时间最长的）
            main_speaker = max(speaker_durations.items(), key=lambda x: x[1])[0]
            main_speaker_duration = speaker_durations[main_speaker]
            main_speaker_segments = speaker_segments[main_speaker]

            # 获取音频总时长
            audio_duration = max([segment.end for segment, _, _ in diarization.itertracks(yield_label=True)])

            # 合并相近的片段
            merged_segments = self._merge_close_segments(main_speaker_segments, gap_threshold=1.0)

            processing_time = (datetime.now() - start_time).total_seconds()

            result = {
                'success': True,
                'num_speakers': num_speakers,
                'speakers': speakers,
                'main_speaker': main_speaker,
                'speaker_durations': speaker_durations,
                'main_speaker_segments': merged_segments,
                'audio_duration': audio_duration,
                'processing_time': processing_time,
                'raw_diarization': diarization
            }

            logger.info(f"官方pyannote说话人分离完成，用时 {processing_time:.2f} 秒")
            logger.info(f"主要说话人: {main_speaker}, 时长: {main_speaker_duration:.2f}秒")
            logger.info(f"主要说话人片段数: {len(merged_segments)}")

            return result

        except Exception as e:
            logger.error(f"说话人分离失败: {e}")
            return None

    def _merge_close_segments(self, segments, gap_threshold=1.0):
        """合并相近的片段"""
        if not segments:
            return []

        # 按开始时间排序
        sorted_segments = sorted(segments, key=lambda x: x[0])
        merged = [sorted_segments[0]]

        for current_start, current_end in sorted_segments[1:]:
            last_start, last_end = merged[-1]

            # 如果当前片段与上一个片段的间隔小于阈值，则合并
            if current_start - last_end <= gap_threshold:
                merged[-1] = (last_start, max(last_end, current_end))
            else:
                merged.append((current_start, current_end))

        return merged

    def save_diarization_rttm(self, diarization, output_path):
        """保存分离结果为RTTM格式"""
        try:
            with open(output_path, 'w') as f:
                for segment, _, speaker in diarization.itertracks(yield_label=True):
                    f.write(f"SPEAKER audio 1 {segment.start:.3f} {segment.end - segment.start:.3f} <NA> <NA> {speaker} <NA> <NA>\n")
            logger.info(f"RTTM文件已保存: {output_path}")
        except Exception as e:
            logger.error(f"保存RTTM文件失败: {e}")

def test_official_pyannote():
    """测试官方pyannote系统"""
    print("=" * 80)
    print("测试官方pyannote说话人分离系统")
    print("使用HuggingFace Token访问高质量预训练模型")
    print("=" * 80)

    # 检查音频文件
    audio_file = "../data/机场地勤音频.WAV"
    if not os.path.exists(audio_file):
        print(f"错误: 音频文件不存在 - {audio_file}")
        return

    # 创建系统
    system = OfficialPyAnnoteDiarization()

    # 初始化
    if not system.initialize():
        print("系统初始化失败")
        return

    # 处理音频
    result = system.process_audio(audio_file)

    if result:
        print(f"\n处理结果:")
        print(f"检测到说话人数: {result['num_speakers']}")
        print(f"说话人列表: {result['speakers']}")
        print(f"主要说话人: {result['main_speaker']}")
        print(f"音频总时长: {result['audio_duration']:.2f}秒")
        print(f"处理时间: {result['processing_time']:.2f}秒")

        print(f"\n说话人时长统计:")
        for speaker, duration in result['speaker_durations'].items():
            percentage = (duration / result['audio_duration']) * 100
            marker = " (主要说话人)" if speaker == result['main_speaker'] else ""
            print(f"  {speaker}: {duration:.2f}秒 ({percentage:.1f}%){marker}")

        print(f"\n主要说话人片段:")
        for i, (start, end) in enumerate(result['main_speaker_segments'], 1):
            print(f"  片段{i}: {start:.1f}s - {end:.1f}s (时长: {end-start:.1f}s)")

        # 保存详细的分离结果
        if 'raw_diarization' in result:
            rttm_file = f"official_pyannote_diarization_{datetime.now().strftime('%Y%m%d_%H%M%S')}.rttm"
            system.save_diarization_rttm(result['raw_diarization'], rttm_file)
            print(f"\n详细分离结果已保存: {rttm_file}")

    else:
        print("处理失败")

if __name__ == "__main__":
    test_official_pyannote()

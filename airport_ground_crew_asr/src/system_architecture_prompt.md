# 机场地勤ASR系统架构流程图设计提示词

## 系统概述
设计一个基于WhisperX和pyannote.audio的机场地勤语音识别系统流程图，该系统专门用于处理机场地勤人员的语音通信，实现说话人分离和语音转录功能。

## 核心技术栈
- **WhisperX**: 主控流程框架，集成ASR和说话人分离
- **pyannote.audio**: 说话人分离(Speaker Diarization)和语音活动检测(VAD)
- **OpenAI Whisper**: 自动语音识别(ASR)模型，使用large-v3版本

## 系统流程设计要求

### 1. 输入阶段
- **音频输入**: 支持多种格式(MP3, WAV, M4A等)
- **来源**: 机场地勤通信设备录音
- **特点**: 可能包含多个说话人的对话

### 2. 预处理阶段
- **pyannote.audio模块**:
  - 语音活动检测(VAD): 识别语音和非语音片段
  - 说话人分离(Speaker Diarization): 区分不同说话人
  - 输出: 带时间戳和说话人标签的语音片段列表

### 3. 语音片段处理
- **语音片段提取**: 根据Diarization结果切割原始音频
- **片段标注**: 每个片段包含开始时间、结束时间、说话人ID

### 4. 语音识别阶段
- **OpenAI Whisper处理**:
  - 对每个语音片段单独进行ASR处理
  - 使用large-v3模型确保高精度
  - 生成带时间戳的转录文本

### 5. 结果整合阶段
- **数据合并**: 整合说话人信息、转录文本、时间戳
- **格式化输出**: 生成标准化的转录结果

### 6. 输出阶段
- **多格式支持**: SRT字幕、JSON数据、VTT格式等
- **结构化数据**: 包含说话人标识、时间戳、转录内容

## 视觉设计要求

### 布局结构
- 采用从左到右的流程布局
- 主流程容器使用虚线边框标识WhisperX主控范围
- 清晰的模块分层和数据流向

### 颜色编码
- **输入/输出模块**: 浅蓝绿色背景 (#e8f6f8)
- **处理模块**: 浅蓝色背景 (#e3f2fd)  
- **数据存储**: 浅黄色背景 (#fffde7)
- **核心技术**: 使用醒目的蓝色高亮 (#1e88e5)

### 连接线设计
- 使用带箭头的流程线表示数据流向
- 在连接线上标注数据类型和处理说明
- 采用柔和的灰色调 (#78909c)

### 文字标注
- 主要模块名称使用粗体高亮
- 技术细节使用小字体说明
- 数据流标签清晰标识传递内容

## 专业特性
- 针对机场地勤场景优化的语音处理流程
- 支持多说话人环境下的精确识别
- 实时处理能力和高准确率要求
- 标准化输出格式便于后续分析

## 技术亮点
- WhisperX集成框架提供端到端解决方案
- pyannote.audio提供业界领先的说话人分离技术
- OpenAI Whisper large-v3确保转录精度
- 模块化设计支持灵活配置和扩展

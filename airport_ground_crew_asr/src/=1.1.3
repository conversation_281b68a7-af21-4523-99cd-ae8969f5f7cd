Collecting funasr
  Downloading funasr-1.2.6-py3-none-any.whl.metadata (32 kB)
Requirement already satisfied: scipy>=1.4.1 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from funasr) (1.15.2)
Requirement already satisfied: librosa in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from funasr) (0.10.2.post1)
Collecting jamo (from funasr)
  Downloading jamo-0.4.1-py3-none-any.whl.metadata (2.3 kB)
Requirement already satisfied: PyYAML>=5.1.2 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from funasr) (6.0.2)
Requirement already satisfied: soundfile>=0.12.1 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from funasr) (0.13.1)
Collecting kaldiio>=2.17.0 (from funasr)
  Downloading kaldiio-2.18.1-py3-none-any.whl.metadata (13 kB)
Collecting torch-complex (from funasr)
  Downloading torch_complex-0.4.4-py3-none-any.whl.metadata (3.1 kB)
Requirement already satisfied: sentencepiece in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages/sentencepiece-0.2.1-py3.13-macosx-10.13-universal2.egg (from funasr) (0.2.1)
Collecting jieba (from funasr)
  Downloading jieba-0.42.1.tar.gz (19.2 MB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 19.2/19.2 MB 3.4 MB/s eta 0:00:00
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting pytorch-wpe (from funasr)
  Downloading pytorch_wpe-0.0.1-py3-none-any.whl.metadata (242 bytes)
Collecting editdistance>=0.5.2 (from funasr)
  Downloading editdistance-0.8.1.tar.gz (50 kB)
  Installing build dependencies: started
  Installing build dependencies: finished with status 'done'
  Getting requirements to build wheel: started
  Getting requirements to build wheel: finished with status 'done'
  Installing backend dependencies: started
  Installing backend dependencies: finished with status 'done'
  Preparing metadata (pyproject.toml): started
  Preparing metadata (pyproject.toml): finished with status 'done'
Collecting oss2 (from funasr)
  Downloading oss2-2.19.1.tar.gz (298 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: tqdm in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from funasr) (4.67.1)
Collecting umap-learn (from funasr)
  Downloading umap_learn-0.5.7-py3-none-any.whl.metadata (21 kB)
Collecting jaconv (from funasr)
  Downloading jaconv-0.4.0.tar.gz (17 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: hydra-core>=1.3.2 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from funasr) (1.3.2)
Collecting tensorboardX (from funasr)
  Using cached tensorboardX-*******-py2.py3-none-any.whl.metadata (5.8 kB)
Requirement already satisfied: requests in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from funasr) (2.32.3)
Requirement already satisfied: modelscope in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from funasr) (1.26.0)
Requirement already satisfied: omegaconf<2.4,>=2.2 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from hydra-core>=1.3.2->funasr) (2.3.0)
Requirement already satisfied: antlr4-python3-runtime==4.9.* in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from hydra-core>=1.3.2->funasr) (4.9.3)
Requirement already satisfied: packaging in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from hydra-core>=1.3.2->funasr) (24.2)
Requirement already satisfied: numpy in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from kaldiio>=2.17.0->funasr) (2.1.3)
Requirement already satisfied: cffi>=1.0 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from soundfile>=0.12.1->funasr) (1.17.1)
Requirement already satisfied: pycparser in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from cffi>=1.0->soundfile>=0.12.1->funasr) (2.22)
Requirement already satisfied: audioread>=2.1.9 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from librosa->funasr) (3.0.1)
Requirement already satisfied: scikit-learn>=0.20.0 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from librosa->funasr) (1.6.1)
Requirement already satisfied: joblib>=0.14 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from librosa->funasr) (1.4.2)
Requirement already satisfied: decorator>=4.3.0 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from librosa->funasr) (5.2.1)
Requirement already satisfied: numba>=0.51.0 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from librosa->funasr) (0.61.0)
Requirement already satisfied: pooch>=1.1 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from librosa->funasr) (1.8.2)
Requirement already satisfied: soxr>=0.3.2 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from librosa->funasr) (0.5.0.post1)
Requirement already satisfied: typing-extensions>=4.1.1 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from librosa->funasr) (4.12.2)
Requirement already satisfied: lazy-loader>=0.1 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from librosa->funasr) (0.4)
Requirement already satisfied: msgpack>=1.0 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from librosa->funasr) (1.1.0)
Requirement already satisfied: llvmlite<0.45,>=0.44.0dev0 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from numba>=0.51.0->librosa->funasr) (0.44.0)
Requirement already satisfied: platformdirs>=2.5.0 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from pooch>=1.1->librosa->funasr) (4.3.6)
Requirement already satisfied: charset-normalizer<4,>=2 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from requests->funasr) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from requests->funasr) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from requests->funasr) (2.3.0)
Requirement already satisfied: certifi>=2017.4.17 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from requests->funasr) (2025.1.31)
Requirement already satisfied: threadpoolctl>=3.1.0 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from scikit-learn>=0.20.0->librosa->funasr) (3.5.0)
Collecting crcmod>=1.7 (from oss2->funasr)
  Downloading crcmod-1.7.tar.gz (89 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Collecting pycryptodome>=3.4.7 (from oss2->funasr)
  Downloading pycryptodome-3.23.0-cp37-abi3-macosx_10_9_universal2.whl.metadata (3.4 kB)
Collecting aliyun-python-sdk-kms>=2.4.1 (from oss2->funasr)
  Downloading aliyun_python_sdk_kms-2.16.5-py2.py3-none-any.whl.metadata (1.5 kB)
Collecting aliyun-python-sdk-core>=2.13.12 (from oss2->funasr)
  Downloading aliyun-python-sdk-core-2.16.0.tar.gz (449 kB)
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: six in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from oss2->funasr) (1.17.0)
Collecting jmespath<1.0.0,>=0.9.3 (from aliyun-python-sdk-core>=2.13.12->oss2->funasr)
  Downloading jmespath-0.10.0-py2.py3-none-any.whl.metadata (8.0 kB)
Collecting cryptography>=3.0.0 (from aliyun-python-sdk-core>=2.13.12->oss2->funasr)
  Downloading cryptography-45.0.3-cp311-abi3-macosx_10_9_universal2.whl.metadata (5.7 kB)
Requirement already satisfied: protobuf>=3.20 in /Users/<USER>/Desktop/python-project/venv/lib/python3.13/site-packages (from tensorboardX->funasr) (6.31.0)
Collecting pynndescent>=0.5 (from umap-learn->funasr)
  Downloading pynndescent-0.5.13-py3-none-any.whl.metadata (6.8 kB)
Downloading funasr-1.2.6-py3-none-any.whl (701 kB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 701.6/701.6 kB 3.1 MB/s eta 0:00:00
Downloading kaldiio-2.18.1-py3-none-any.whl (29 kB)
Downloading jamo-0.4.1-py3-none-any.whl (9.5 kB)
Downloading jmespath-0.10.0-py2.py3-none-any.whl (24 kB)
Downloading aliyun_python_sdk_kms-2.16.5-py2.py3-none-any.whl (99 kB)
Downloading cryptography-45.0.3-cp311-abi3-macosx_10_9_universal2.whl (7.1 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 7.1/7.1 MB 3.3 MB/s eta 0:00:00
Downloading pycryptodome-3.23.0-cp37-abi3-macosx_10_9_universal2.whl (2.5 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 2.5/2.5 MB 4.1 MB/s eta 0:00:00
Downloading pytorch_wpe-0.0.1-py3-none-any.whl (8.1 kB)
Using cached tensorboardX-*******-py2.py3-none-any.whl (101 kB)
Downloading torch_complex-0.4.4-py3-none-any.whl (9.1 kB)
Downloading umap_learn-0.5.7-py3-none-any.whl (88 kB)
Downloading pynndescent-0.5.13-py3-none-any.whl (56 kB)
Building wheels for collected packages: editdistance, jaconv, jieba, oss2, aliyun-python-sdk-core, crcmod
  Building wheel for editdistance (pyproject.toml): started
  Building wheel for editdistance (pyproject.toml): finished with status 'done'
  Created wheel for editdistance: filename=editdistance-0.8.1-cp313-cp313-macosx_14_0_arm64.whl size=115075 sha256=bbc9f66f468d11e9b4ddc63cf95adcf1d40f3a3c70756d909530c5e4f30e4f8b
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/5d/f0/83/35579f35f97d5b90384045f587f969100fd0ee02b80fe95317
  Building wheel for jaconv (setup.py): started
  Building wheel for jaconv (setup.py): finished with status 'done'
  Created wheel for jaconv: filename=jaconv-0.4.0-py3-none-any.whl size=18280 sha256=cc44ebdc0b7ff98624bb7f3408595f14556999ff817974c91531fef606bb7351
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/68/1e/ec/091544e17cb3d92e216dac6d07ccbc5b782c06b98d40829c0a
  Building wheel for jieba (setup.py): started
  Building wheel for jieba (setup.py): finished with status 'done'
  Created wheel for jieba: filename=jieba-0.42.1-py3-none-any.whl size=19314508 sha256=225ca5bcb7401fba83c1a8de10537493843dee58104025ffe0a031d4108e8cdd
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/8d/e9/51/2f0a6a9d051293af20e265d3889beae50efe2de72f8511c801
  Building wheel for oss2 (setup.py): started
  Building wheel for oss2 (setup.py): finished with status 'done'
  Created wheel for oss2: filename=oss2-2.19.1-py3-none-any.whl size=124001 sha256=60257175b5558e7996bd16ca1ef0cbba8d7b6ac5852d8d6567540747f9196ac8
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/68/a5/c4/ae8c461fdb669b208f748dd02fab0d28c052edbb3333394c8c
  Building wheel for aliyun-python-sdk-core (setup.py): started
  Building wheel for aliyun-python-sdk-core (setup.py): finished with status 'done'
  Created wheel for aliyun-python-sdk-core: filename=aliyun_python_sdk_core-2.16.0-py3-none-any.whl size=535381 sha256=4f5724f5a72fea78aed36d25ec83733e219e4682012a061bf03c99ac0e696f16
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/b5/ba/72/618a9c3b436edb6c2cc25e6e3b41eb298cf8406e234a1e0a18
  Building wheel for crcmod (setup.py): started
  Building wheel for crcmod (setup.py): finished with status 'done'
  Created wheel for crcmod: filename=crcmod-1.7-cp313-cp313-macosx_10_13_universal2.whl size=25259 sha256=97e2a4d66cea3a79ef9fa7c54bbf03ffae6855f886112cfc5c69ec43fe92b9b0
  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/c5/b4/7d/51d3485a8022501c7f7dba78b6c945088ff93d40142c9a96f4
Successfully built editdistance jaconv jieba oss2 aliyun-python-sdk-core crcmod
Installing collected packages: jieba, jamo, jaconv, crcmod, torch-complex, tensorboardX, pytorch-wpe, pycryptodome, kaldiio, jmespath, editdistance, cryptography, pynndescent, aliyun-python-sdk-core, umap-learn, aliyun-python-sdk-kms, oss2, funasr

Successfully installed aliyun-python-sdk-core-2.16.0 aliyun-python-sdk-kms-2.16.5 crcmod-1.7 cryptography-45.0.3 editdistance-0.8.1 funasr-1.2.6 jaconv-0.4.0 jamo-0.4.1 jieba-0.42.1 jmespath-0.10.0 kaldiio-2.18.1 oss2-2.19.1 pycryptodome-3.23.0 pynndescent-0.5.13 pytorch-wpe-0.0.1 tensorboardX-******* torch-complex-0.4.4 umap-learn-0.5.7

<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>机场地勤ASR系统架构图 - 专业版</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", "Microsoft YaHei", Arial, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      .header {
        text-align: center;
        color: white;
        margin-bottom: 30px;
      }

      .header h1 {
        font-size: 32px;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      }

      .header p {
        font-size: 18px;
        opacity: 0.9;
        font-weight: 300;
      }

      .diagram-container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
        padding: 30px;
        max-width: 1400px;
        width: 100%;
      }

      svg {
        width: 100%;
        height: auto;
        display: block;
      }

      /* SVG 样式 */
      .input-output {
        fill: #e8f5e8;
        stroke: #4caf50;
        stroke-width: 3;
        rx: 15;
        filter: url(#shadow);
      }

      .process {
        fill: #e3f2fd;
        stroke: #2196f3;
        stroke-width: 3;
        rx: 12;
        filter: url(#shadow);
      }

      .data-storage {
        fill: #fff3e0;
        stroke: #ff9800;
        stroke-width: 3;
        rx: 12;
        filter: url(#shadow);
      }

      .core-tech {
        fill: #f3e5f5;
        stroke: #9c27b0;
        stroke-width: 4;
        rx: 15;
        filter: url(#shadow);
      }

      .title {
        font-family: "Segoe UI", sans-serif;
        font-size: 20px;
        font-weight: 700;
        fill: #1a237e;
        text-anchor: middle;
      }

      .main-label {
        font-family: "Segoe UI", sans-serif;
        font-size: 16px;
        font-weight: 600;
        fill: #263238;
        text-anchor: middle;
      }

      .sub-label {
        font-family: "Segoe UI", sans-serif;
        font-size: 12px;
        fill: #546e7a;
        text-anchor: middle;
      }

      .tech-name {
        font-family: "Segoe UI", sans-serif;
        font-size: 18px;
        font-weight: 700;
        fill: #6a1b9a;
        text-anchor: middle;
      }

      .flow-arrow {
        stroke: #455a64;
        stroke-width: 3;
        fill: none;
        marker-end: url(#arrowhead);
      }

      .flow-text {
        font-family: "Segoe UI", sans-serif;
        font-size: 11px;
        font-weight: 500;
        fill: #37474f;
        text-anchor: middle;
      }

      .stage-label {
        font-family: "Segoe UI", sans-serif;
        font-size: 14px;
        font-weight: 600;
        fill: #1976d2;
        text-anchor: middle;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>🛫 机场地勤ASR系统架构图</h1>
      <p>基于 WhisperX 和 pyannote.audio 的智能语音识别解决方案</p>
    </div>

    <div class="diagram-container">
      <svg viewBox="0 0 1300 900">
        <defs>
          <!-- 箭头定义 -->
          <marker
            id="arrowhead"
            markerWidth="12"
            markerHeight="8"
            refX="10"
            refY="4"
            orient="auto"
          >
            <polygon points="0 0, 12 4, 0 8" fill="#455a64" />
          </marker>

          <!-- 阴影滤镜 -->
          <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
            <feDropShadow
              dx="3"
              dy="3"
              stdDeviation="4"
              flood-color="rgba(0,0,0,0.2)"
            />
          </filter>

          <!-- 渐变背景 -->
          <linearGradient id="headerGrad" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color: #1976d2" />
            <stop offset="100%" style="stop-color: #42a5f5" />
          </linearGradient>
        </defs>

        <!-- 系统标题 -->
        <rect
          x="50"
          y="30"
          width="1200"
          height="70"
          fill="url(#headerGrad)"
          rx="20"
          filter="url(#shadow)"
        />
        <text x="650" y="75" class="title" fill="white">
          机场地勤ASR系统 - WhisperX 集成架构
        </text>

        <!-- 阶段标识 -->
        <text x="200" y="140" class="stage-label">📥 输入阶段</text>
        <text x="450" y="140" class="stage-label">🔍 预处理阶段</text>
        <text x="750" y="140" class="stage-label">🎯 识别阶段</text>
        <text x="1050" y="140" class="stage-label">📤 输出阶段</text>

        <!-- 1. 音频输入模块 -->
        <rect x="120" y="180" width="160" height="120" class="input-output" />
        <text x="200" y="210" class="main-label">🎤 音频输入</text>
        <text x="200" y="230" class="sub-label">机场地勤通信录音</text>
        <text x="200" y="250" class="sub-label">格式: MP3, WAV, M4A</text>
        <text x="200" y="270" class="sub-label">特点: 多说话人环境</text>
        <text x="200" y="290" class="sub-label">实时/批量处理</text>

        <!-- 2. pyannote.audio 核心模块 -->
        <rect x="350" y="160" width="200" height="160" class="core-tech" />
        <text x="450" y="190" class="tech-name">pyannote.audio</text>
        <text x="450" y="215" class="main-label">🔍 语音活动检测 (VAD)</text>
        <text x="450" y="235" class="main-label">👥 说话人分离</text>
        <text x="450" y="255" class="sub-label">Speaker Diarization</text>
        <text x="450" y="275" class="sub-label">时间戳精确定位</text>
        <text x="450" y="295" class="sub-label">多说话人识别算法</text>
        <text x="450" y="315" class="sub-label">深度学习模型</text>

        <!-- 3. 分离结果数据 -->
        <rect x="620" y="180" width="180" height="120" class="data-storage" />
        <text x="710" y="210" class="main-label">📊 分离结果</text>
        <text x="710" y="230" class="main-label">语音片段列表</text>
        <text x="710" y="250" class="sub-label">开始时间 | 结束时间</text>
        <text x="710" y="270" class="sub-label">说话人ID标识</text>
        <text x="710" y="290" class="sub-label">置信度评分</text>

        <!-- 4. 语音片段提取 -->
        <rect x="620" y="380" width="180" height="100" class="process" />
        <text x="710" y="410" class="main-label">✂️ 片段提取</text>
        <text x="710" y="430" class="sub-label">根据时间戳切割</text>
        <text x="710" y="450" class="sub-label">保持音频质量</text>
        <text x="710" y="470" class="sub-label">批量处理优化</text>

        <!-- 5. OpenAI Whisper ASR -->
        <rect x="870" y="350" width="200" height="160" class="core-tech" />
        <text x="970" y="380" class="tech-name">OpenAI Whisper</text>
        <text x="970" y="405" class="main-label">🎯 语音识别引擎</text>
        <text x="970" y="425" class="sub-label">模型: large-v3</text>
        <text x="970" y="445" class="sub-label">高精度转录</text>
        <text x="970" y="465" class="sub-label">多语言支持</text>
        <text x="970" y="485" class="sub-label">并行处理能力</text>
        <text x="970" y="505" class="sub-label">实时推理优化</text>

        <!-- 6. ASR转录结果 -->
        <rect x="870" y="550" width="200" height="100" class="data-storage" />
        <text x="970" y="580" class="main-label">📝 转录文本</text>
        <text x="970" y="600" class="sub-label">带时间戳文本</text>
        <text x="970" y="620" class="sub-label">置信度评分</text>
        <text x="970" y="640" class="sub-label">语言检测结果</text>

        <!-- 7. 结果整合处理 -->
        <rect x="620" y="580" width="180" height="120" class="process" />
        <text x="710" y="610" class="main-label">🔄 结果整合</text>
        <text x="710" y="630" class="sub-label">合并说话人信息</text>
        <text x="710" y="650" class="sub-label">时间轴对齐</text>
        <text x="710" y="670" class="sub-label">格式标准化</text>
        <text x="710" y="690" class="sub-label">质量验证</text>

        <!-- 8. 最终输出 -->
        <rect x="350" y="620" width="200" height="120" class="input-output" />
        <text x="450" y="650" class="main-label">📤 输出文件</text>
        <text x="450" y="670" class="sub-label">SRT字幕文件</text>
        <text x="450" y="690" class="sub-label">JSON结构化数据</text>
        <text x="450" y="710" class="sub-label">VTT网页字幕</text>
        <text x="450" y="730" class="sub-label">CSV分析报表</text>

        <!-- 9. 应用场景 -->
        <rect x="120" y="620" width="160" height="120" class="input-output" />
        <text x="200" y="650" class="main-label">🛫 应用场景</text>
        <text x="200" y="670" class="sub-label">地勤通信监控</text>
        <text x="200" y="690" class="sub-label">安全事件分析</text>
        <text x="200" y="710" class="sub-label">培训质量评估</text>
        <text x="200" y="730" class="sub-label">合规性检查</text>

        <!-- 数据流箭头连接 -->

        <!-- 1. 音频输入 -> pyannote.audio -->
        <path d="M280 240 L350 240" class="flow-arrow" />
        <text x="315" y="230" class="flow-text">原始音频流</text>

        <!-- 2. pyannote.audio -> 分离结果 -->
        <path d="M550 240 L620 240" class="flow-arrow" />
        <text x="585" y="230" class="flow-text">分离数据</text>

        <!-- 3. 分离结果 -> 片段提取 -->
        <path d="M710 300 L710 380" class="flow-arrow" />
        <text x="730" y="340" class="flow-text">时间戳</text>

        <!-- 4. 音频输入 -> 片段提取 (原始音频) -->
        <path d="M200 300 Q200 340 620 430" class="flow-arrow" />
        <text x="410" y="365" class="flow-text">原始音频数据</text>

        <!-- 5. 片段提取 -> Whisper ASR -->
        <path d="M800 430 L870 430" class="flow-arrow" />
        <text x="835" y="420" class="flow-text">音频片段</text>

        <!-- 6. Whisper ASR -> 转录结果 -->
        <path d="M970 510 L970 550" class="flow-arrow" />
        <text x="990" y="530" class="flow-text">文本输出</text>

        <!-- 7. 分离结果 -> 结果整合 (说话人信息) -->
        <path d="M710 300 Q710 440 710 580" class="flow-arrow" />
        <text x="730" y="440" class="flow-text">说话人标签</text>

        <!-- 8. 转录结果 -> 结果整合 -->
        <path d="M870 600 L800 640" class="flow-arrow" />
        <text x="835" y="610" class="flow-text">转录文本</text>

        <!-- 9. 结果整合 -> 输出文件 -->
        <path d="M620 680 L550 680" class="flow-arrow" />
        <text x="585" y="670" class="flow-text">最终结果</text>

        <!-- 10. 输出文件 -> 应用场景 -->
        <path d="M350 680 L280 680" class="flow-arrow" />
        <text x="315" y="670" class="flow-text">实际应用</text>

        <!-- 技术特性说明 -->
        <rect
          x="120"
          y="780"
          width="950"
          height="60"
          fill="#f8f9fa"
          stroke="#dee2e6"
          stroke-width="2"
          rx="10"
        />
        <text x="595" y="805" class="main-label" fill="#495057">
          🔧 核心技术特性
        </text>
        <text x="200" y="825" class="sub-label">• 实时处理能力</text>
        <text x="350" y="825" class="sub-label">• 高精度识别</text>
        <text x="480" y="825" class="sub-label">• 多说话人分离</text>
        <text x="620" y="825" class="sub-label">• 标准化输出</text>
        <text x="750" y="825" class="sub-label">• 模块化架构</text>
        <text x="880" y="825" class="sub-label">• 企业级应用</text>
      </svg>
    </div>
  </body>
</html>

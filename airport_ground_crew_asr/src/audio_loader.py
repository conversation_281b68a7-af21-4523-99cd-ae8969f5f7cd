
"""
通用音频加载器
支持多种音频格式，避免aifc依赖
"""

import numpy as np

def load_audio(file_path, sr=16000):
    """
    加载音频文件
    优先使用librosa，备用soundfile和pydub
    """
    
    # 方法1: librosa
    try:
        import librosa
        wav, _ = librosa.load(file_path, sr=sr)
        return wav
    except ImportError:
        pass
    except Exception as e:
        print(f"librosa加载失败: {e}")
    
    # 方法2: soundfile
    try:
        import soundfile as sf
        wav, original_sr = sf.read(file_path)
        
        # 重采样到目标采样率
        if original_sr != sr:
            import scipy.signal
            wav = scipy.signal.resample(wav, int(len(wav) * sr / original_sr))
        
        return wav.astype(np.float32)
    except ImportError:
        pass
    except Exception as e:
        print(f"soundfile加载失败: {e}")
    
    # 方法3: pydub
    try:
        from pydub import AudioSegment
        audio = AudioSegment.from_file(file_path)
        
        # 转换为单声道
        if audio.channels > 1:
            audio = audio.set_channels(1)
        
        # 设置采样率
        audio = audio.set_frame_rate(sr)
        
        # 转换为numpy数组
        wav = np.array(audio.get_array_of_samples(), dtype=np.float32)
        wav = wav / (2**15)  # 标准化到[-1, 1]
        
        return wav
    except ImportError:
        pass
    except Exception as e:
        print(f"pydub加载失败: {e}")
    
    # 方法4: wave (仅支持WAV文件)
    try:
        import wave
        with wave.open(file_path, 'rb') as wf:
            frames = wf.readframes(wf.getnframes())
            wav = np.frombuffer(frames, dtype=np.int16).astype(np.float32)
            wav = wav / (2**15)  # 标准化到[-1, 1]
            
            # 简单重采样（如果需要）
            original_sr = wf.getframerate()
            if original_sr != sr:
                # 简单的线性插值重采样
                ratio = sr / original_sr
                new_length = int(len(wav) * ratio)
                wav = np.interp(np.linspace(0, len(wav)-1, new_length), 
                               np.arange(len(wav)), wav)
            
            return wav
    except ImportError:
        pass
    except Exception as e:
        print(f"wave加载失败: {e}")
    
    raise RuntimeError(f"无法加载音频文件: {file_path}")

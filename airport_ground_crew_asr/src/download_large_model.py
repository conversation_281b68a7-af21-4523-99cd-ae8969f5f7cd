#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载Whisper Large-v2模型
利用翻墙环境下载更大更准确的模型
"""

import os
import sys
import requests
import logging
from pathlib import Path
import subprocess
from tqdm import tqdm

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def download_file_with_progress(url, output_path):
    """带进度条的文件下载"""
    try:
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        
        with open(output_path, 'wb') as f, tqdm(
            desc=os.path.basename(output_path),
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
        ) as pbar:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    pbar.update(len(chunk))
        
        return True
    except Exception as e:
        logger.error(f"下载失败: {e}")
        return False

def download_whisper_large_v2():
    """下载Whisper Large-v2模型"""
    
    # 创建模型目录
    model_dir = Path.home() / ".cache" / "huggingface" / "hub" / "models--Systran--faster-whisper-large-v2"
    model_dir.mkdir(parents=True, exist_ok=True)
    
    # Large-v2模型文件列表
    model_files = [
        {
            "filename": "config.json",
            "url": "https://huggingface.co/Systran/faster-whisper-large-v2/resolve/main/config.json"
        },
        {
            "filename": "model.bin",
            "url": "https://huggingface.co/Systran/faster-whisper-large-v2/resolve/main/model.bin"
        },
        {
            "filename": "tokenizer.json", 
            "url": "https://huggingface.co/Systran/faster-whisper-large-v2/resolve/main/tokenizer.json"
        },
        {
            "filename": "vocabulary.txt",
            "url": "https://huggingface.co/Systran/faster-whisper-large-v2/resolve/main/vocabulary.txt"
        }
    ]
    
    print("开始下载Whisper Large-v2模型...")
    print(f"目标目录: {model_dir}")
    
    success_count = 0
    
    for file_info in model_files:
        filename = file_info["filename"]
        url = file_info["url"]
        output_path = model_dir / filename
        
        if output_path.exists():
            print(f"✓ 文件已存在: {filename}")
            success_count += 1
            continue
        
        print(f"\n下载: {filename}")
        print(f"URL: {url}")
        
        if download_file_with_progress(url, output_path):
            print(f"✓ 下载成功: {filename}")
            success_count += 1
        else:
            print(f"✗ 下载失败: {filename}")
    
    if success_count == len(model_files):
        print(f"\n🎉 Whisper Large-v2模型下载完成!")
        print(f"所有 {len(model_files)} 个文件下载成功")
        return True
    else:
        print(f"\n⚠️ 部分文件下载失败")
        print(f"成功: {success_count}/{len(model_files)}")
        return False

def test_model_loading():
    """测试模型加载"""
    try:
        print("\n测试模型加载...")
        from faster_whisper import WhisperModel
        
        model = WhisperModel("large-v2", device="cpu", compute_type="int8")
        print("✓ Large-v2模型加载成功!")
        
        # 释放模型
        del model
        return True
        
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return False

def download_whisper_large_v3():
    """下载Whisper Large-v3模型（最新版本）"""
    
    # 创建模型目录
    model_dir = Path.home() / ".cache" / "huggingface" / "hub" / "models--Systran--faster-whisper-large-v3"
    model_dir.mkdir(parents=True, exist_ok=True)
    
    # Large-v3模型文件列表
    model_files = [
        {
            "filename": "config.json",
            "url": "https://huggingface.co/Systran/faster-whisper-large-v3/resolve/main/config.json"
        },
        {
            "filename": "model.bin",
            "url": "https://huggingface.co/Systran/faster-whisper-large-v3/resolve/main/model.bin"
        },
        {
            "filename": "tokenizer.json", 
            "url": "https://huggingface.co/Systran/faster-whisper-large-v3/resolve/main/tokenizer.json"
        },
        {
            "filename": "vocabulary.txt",
            "url": "https://huggingface.co/Systran/faster-whisper-large-v3/resolve/main/vocabulary.txt"
        }
    ]
    
    print("开始下载Whisper Large-v3模型...")
    print(f"目标目录: {model_dir}")
    
    success_count = 0
    
    for file_info in model_files:
        filename = file_info["filename"]
        url = file_info["url"]
        output_path = model_dir / filename
        
        if output_path.exists():
            print(f"✓ 文件已存在: {filename}")
            success_count += 1
            continue
        
        print(f"\n下载: {filename}")
        print(f"URL: {url}")
        
        if download_file_with_progress(url, output_path):
            print(f"✓ 下载成功: {filename}")
            success_count += 1
        else:
            print(f"✗ 下载失败: {filename}")
    
    if success_count == len(model_files):
        print(f"\n🎉 Whisper Large-v3模型下载完成!")
        print(f"所有 {len(model_files)} 个文件下载成功")
        return True
    else:
        print(f"\n⚠️ 部分文件下载失败")
        print(f"成功: {success_count}/{len(model_files)}")
        return False

def check_network():
    """检查网络连接"""
    try:
        response = requests.get("https://huggingface.co", timeout=10)
        if response.status_code == 200:
            print("✓ 网络连接正常，可以访问HuggingFace")
            return True
        else:
            print(f"⚠️ HuggingFace访问异常，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 网络连接失败: {e}")
        return False

def main():
    print("=" * 70)
    print("Whisper Large模型下载工具")
    print("=" * 70)
    
    # 检查网络
    if not check_network():
        print("请检查网络连接和翻墙设置")
        return
    
    print("\n选择要下载的模型:")
    print("1. Large-v2 (推荐，稳定版本)")
    print("2. Large-v3 (最新版本，可能更准确)")
    print("3. 两个都下载")
    
    choice = input("\n请选择 (1/2/3): ").strip()
    
    if choice == "1":
        success = download_whisper_large_v2()
        if success:
            test_model_loading()
    elif choice == "2":
        success = download_whisper_large_v3()
        if success:
            test_model_loading()
    elif choice == "3":
        print("\n下载Large-v2...")
        success_v2 = download_whisper_large_v2()
        
        print("\n下载Large-v3...")
        success_v3 = download_whisper_large_v3()
        
        if success_v2 or success_v3:
            test_model_loading()
    else:
        print("无效选择")
        return
    
    print("\n" + "=" * 70)
    print("下载完成！")
    print("现在可以使用improved_asr.py运行高质量ASR了")
    print("建议使用large-v2模型以获得最佳准确性")
    print("=" * 70)

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SenseVoice安装和配置脚本
自动安装SenseVoice及其依赖包
"""

import subprocess
import sys
import os
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_command(command, description):
    """运行命令并记录结果"""
    logger.info(f"执行: {description}")
    logger.info(f"命令: {command}")

    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)

        if result.returncode == 0:
            logger.info(f"✓ {description} - 成功")
            if result.stdout:
                logger.info(f"输出: {result.stdout.strip()}")
            return True
        else:
            logger.error(f"✗ {description} - 失败")
            logger.error(f"错误: {result.stderr.strip()}")
            return False

    except Exception as e:
        logger.error(f"✗ {description} - 异常: {e}")
        return False

def check_package_installed(package_name):
    """检查包是否已安装"""
    try:
        __import__(package_name)
        return True
    except ImportError:
        return False

def install_sensevoice_dependencies():
    """安装SenseVoice依赖包"""
    print("=" * 70)
    print("安装SenseVoice依赖包")
    print("=" * 70)

    # SenseVoice核心依赖包
    packages = [
        "torch<=2.3",
        "torchaudio",
        "modelscope",
        "huggingface_hub",
        "funasr>=1.1.3",
        "numpy<=1.26.4",
        "gradio",
        "fastapi>=0.111.1"
    ]

    success_count = 0
    total_count = len(packages)

    for package in packages:
        package_name = package.split('<=')[0].split('>=')[0].split('==')[0]

        # 检查是否已安装
        if check_package_installed(package_name.replace('-', '_')):
            logger.info(f"✓ {package_name} 已安装")
            success_count += 1
            continue

        # 安装包
        if run_command(f"pip install {package}", f"安装 {package}"):
            success_count += 1
        else:
            logger.warning(f"⚠ {package} 安装失败，但继续执行")

    print(f"\n成功安装 {success_count}/{total_count} 个包")

    if success_count == total_count:
        print("✓ 所有依赖包安装成功")
        return True
    else:
        print("⚠ 部分依赖包安装失败，但可以继续")
        return True

def test_sensevoice_import():
    """测试SenseVoice导入"""
    print("\n" + "=" * 50)
    print("测试SenseVoice导入")
    print("=" * 50)

    try:
        from funasr import AutoModel
        from funasr.utils.postprocess_utils import rich_transcription_postprocess
        print("✓ funasr 导入成功")

        try:
            import modelscope
            print("✓ modelscope 导入成功")
        except ImportError:
            print("✗ modelscope 导入失败")
            return False

        try:
            import torch
            print(f"✓ torch {torch.__version__} 导入成功")
        except ImportError:
            print("✗ torch 导入失败")
            return False

        try:
            import torchaudio
            print(f"✓ torchaudio {torchaudio.__version__} 导入成功")
        except ImportError:
            print("✗ torchaudio 导入失败")
            return False

        return True

    except ImportError as e:
        print(f"✗ SenseVoice导入失败: {e}")
        return False

def download_sensevoice_model():
    """下载SenseVoice模型"""
    print("\n" + "=" * 50)
    print("下载SenseVoice模型")
    print("=" * 50)

    try:
        from funasr import AutoModel

        model_name = "iic/SenseVoiceSmall"
        logger.info(f"开始下载SenseVoice模型: {model_name}")

        # 尝试加载模型（会自动下载） - 修复remote_code问题
        model = AutoModel(
            model=model_name,
            trust_remote_code=False,  # 改为False避免remote code问题
            device="cpu",  # 使用CPU避免GPU相关问题
            disable_update=True,  # 禁用更新检查
        )

        logger.info("✓ SenseVoice模型下载和加载成功")
        return True

    except Exception as e:
        logger.error(f"✗ SenseVoice模型下载失败: {e}")
        logger.info("可能的解决方案:")
        logger.info("1. 检查网络连接")
        logger.info("2. 确保有足够的磁盘空间")
        logger.info("3. 如果在中国大陆，可能需要配置代理")
        return False

def test_sensevoice_functionality():
    """测试SenseVoice基本功能"""
    print("\n" + "=" * 50)
    print("测试SenseVoice基本功能")
    print("=" * 50)

    try:
        from funasr import AutoModel
        from funasr.utils.postprocess_utils import rich_transcription_postprocess

        # 创建模型实例 - 修复remote_code问题
        model = AutoModel(
            model="iic/SenseVoiceSmall",
            trust_remote_code=False,  # 改为False避免remote code问题
            device="cpu",
            disable_update=True,  # 禁用更新检查
        )

        logger.info("✓ SenseVoice模型创建成功")

        # 检查是否有测试音频文件
        test_audio = "../data/机场地勤音频.WAV"
        if os.path.exists(test_audio):
            logger.info(f"找到测试音频文件: {test_audio}")

            # 进行简单的转录测试
            logger.info("开始转录测试...")
            res = model.generate(
                input=test_audio,
                cache={},
                language="auto",
                use_itn=True,
                batch_size_s=60,
            )

            if res and len(res) > 0:
                text = rich_transcription_postprocess(res[0]["text"])
                logger.info(f"✓ 转录测试成功")
                logger.info(f"转录结果: {text[:100]}...")
                return True
            else:
                logger.warning("⚠ 转录结果为空")
                return False
        else:
            logger.info("未找到测试音频文件，跳过功能测试")
            return True

    except Exception as e:
        logger.error(f"✗ SenseVoice功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("SenseVoice安装和配置脚本")
    print("阿里巴巴SenseVoice多语言语音理解模型")
    print("=" * 80)

    success_steps = 0
    total_steps = 4

    # 步骤1: 安装依赖包
    if install_sensevoice_dependencies():
        success_steps += 1

    # 步骤2: 测试导入
    if test_sensevoice_import():
        success_steps += 1

    # 步骤3: 下载模型
    if download_sensevoice_model():
        success_steps += 1

    # 步骤4: 测试功能
    if test_sensevoice_functionality():
        success_steps += 1

    # 总结
    print("\n" + "=" * 80)
    print("安装总结")
    print("=" * 80)

    print(f"完成步骤: {success_steps}/{total_steps}")

    if success_steps == total_steps:
        print("🎉 SenseVoice安装和配置完成！")
        print("\n可以使用的功能:")
        print("- 多语言语音识别 (中文、英文、粤语、日语、韩语)")
        print("- 情感识别")
        print("- 音频事件检测")
        print("- 快速推理 (比Whisper快15倍)")
        print("\n下一步:")
        print("运行: python sensevoice_asr_system.py")
        print("或运行: python integrated_sensevoice_asr_system.py")
    elif success_steps >= 2:
        print("⚠ SenseVoice部分安装成功")
        print("可以尝试运行基本功能，但可能存在问题")
    else:
        print("❌ SenseVoice安装失败")
        print("请检查网络连接和依赖包安装")

    print("\n" + "=" * 80)

if __name__ == "__main__":
    main()

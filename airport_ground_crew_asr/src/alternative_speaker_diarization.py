#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
替代的说话人分离方案
使用其他库实现说话人分离功能
"""

import subprocess
import sys
import os

def install_alternative_packages():
    """安装替代的说话人分离包"""
    print("=" * 70)
    print("安装替代的说话人分离工具")
    print("=" * 70)
    
    packages = [
        # 简单的说话人分离
        "resemblyzer",
        
        # 音频处理基础库
        "librosa",
        "soundfile", 
        "scipy",
        "scikit-learn",
        "matplotlib",
        "seaborn",
        
        # 深度学习框架
        "torch",
        "torchaudio",
        
        # 其他音频工具
        "webrtcvad",  # 语音活动检测
        "pydub",      # 音频处理
        "noisereduce", # 降噪
    ]
    
    success_count = 0
    
    for package in packages:
        print(f"\n安装 {package}...")
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"✓ {package}")
                success_count += 1
            else:
                print(f"✗ {package}: {result.stderr[:100]}...")
        except Exception as e:
            print(f"✗ {package}: {e}")
    
    print(f"\n成功安装 {success_count}/{len(packages)} 个包")
    return success_count > len(packages) // 2

def test_alternative_imports():
    """测试替代库的导入"""
    print("\n" + "="*60)
    print("测试替代库导入")
    print("="*60)
    
    available_tools = []
    
    # 测试resemblyzer
    try:
        from resemblyzer import preprocess_wav, VoiceEncoder
        print("✓ resemblyzer 可用")
        available_tools.append("resemblyzer")
    except ImportError as e:
        print(f"✗ resemblyzer: {e}")
    
    # 测试基础音频库
    try:
        import librosa
        print(f"✓ librosa {librosa.__version__}")
        available_tools.append("librosa")
    except ImportError as e:
        print(f"✗ librosa: {e}")
    
    try:
        import torch
        print(f"✓ torch {torch.__version__}")
        available_tools.append("torch")
    except ImportError as e:
        print(f"✗ torch: {e}")
    
    try:
        import sklearn
        print(f"✓ scikit-learn {sklearn.__version__}")
        available_tools.append("sklearn")
    except ImportError as e:
        print(f"✗ scikit-learn: {e}")
    
    try:
        import webrtcvad
        print("✓ webrtcvad 可用")
        available_tools.append("webrtcvad")
    except ImportError as e:
        print(f"✗ webrtcvad: {e}")
    
    return available_tools

def try_install_pyannote_without_sentencepiece():
    """尝试安装pyannote.audio但跳过sentencepiece依赖"""
    print("\n" + "="*60)
    print("尝试安装pyannote.audio (跳过problematic依赖)")
    print("="*60)
    
    # 方法1: 尝试安装pyannote.core和pyannote.database
    core_packages = [
        "pyannote.core",
        "pyannote.database", 
        "pyannote.metrics",
        "pyannote.pipeline"
    ]
    
    for package in core_packages:
        print(f"安装 {package}...")
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"✓ {package}")
            else:
                print(f"✗ {package}: {result.stderr[:100]}...")
        except Exception as e:
            print(f"✗ {package}: {e}")
    
    # 方法2: 尝试安装pyannote.audio但忽略依赖错误
    print("\n尝试安装pyannote.audio (忽略依赖错误)...")
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "--no-deps", "pyannote.audio"
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✓ pyannote.audio (无依赖)")
        else:
            print(f"✗ pyannote.audio: {result.stderr[:200]}...")
    except Exception as e:
        print(f"✗ pyannote.audio: {e}")
    
    # 测试导入
    try:
        from pyannote.core import Segment, Timeline
        print("✓ pyannote.core 导入成功")
        return True
    except ImportError as e:
        print(f"✗ pyannote.core 导入失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("替代说话人分离方案")
    print("=" * 70)
    
    # 1. 安装替代包
    if install_alternative_packages():
        print("\n✓ 替代包安装成功")
    else:
        print("\n⚠️ 部分替代包安装失败")
    
    # 2. 测试可用工具
    available_tools = test_alternative_imports()
    
    # 3. 尝试安装pyannote组件
    pyannote_available = try_install_pyannote_without_sentencepiece()
    
    # 4. 总结
    print("\n" + "="*70)
    print("安装总结")
    print("="*70)
    
    if available_tools:
        print(f"✓ 可用工具: {', '.join(available_tools)}")
        
        if "resemblyzer" in available_tools:
            print("\n🎉 推荐使用 resemblyzer 进行说话人分离")
            print("resemblyzer 是一个简单易用的说话人分离库")
        
        if "librosa" in available_tools and "sklearn" in available_tools:
            print("\n🎉 可以使用 librosa + sklearn 实现基础说话人分离")
            print("基于音频特征的聚类方法")
        
        if pyannote_available:
            print("\n🎉 pyannote.core 可用，可以使用基础功能")
        
        print("\n现在可以创建说话人分离系统了！")
        
    else:
        print("⚠️ 没有可用的说话人分离工具")
        print("建议检查网络连接和Python环境")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
说话人分离系统
使用pyannote-audio实现高质量的说话人分离
"""

import os
import sys
import logging
import warnings
import numpy as np
import json
from datetime import datetime
from pathlib import Path

# 抑制警告
warnings.filterwarnings("ignore")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# HuggingFace Token
HF_TOKEN = "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"

# 设置环境变量
os.environ['HF_TOKEN'] = HF_TOKEN

# 导入pyannote依赖
try:
    from pyannote.audio import Pipeline
    from huggingface_hub import login

    SPEAKER_DIARIZATION_AVAILABLE = True
    logger.info("pyannote说话人分离工具可用")
except ImportError as e:
    SPEAKER_DIARIZATION_AVAILABLE = False
    logger.error(f"pyannote说话人分离工具导入失败: {e}")

class SpeakerDiarizationSystem:
    """基于pyannote的说话人分离系统"""

    def __init__(self):
        self.pipeline = None
        self.model_name = None
        logger.info("初始化pyannote说话人分离系统")

    def initialize(self):
        """初始化pyannote pipeline"""
        try:
            if not SPEAKER_DIARIZATION_AVAILABLE:
                raise ImportError("pyannote说话人分离工具不可用")

            logger.info("加载pyannote说话人分离pipeline...")

            # 按优先级尝试不同模型
            models_to_try = [
                "pyannote/speaker-diarization-3.1",
                "pyannote/speaker-diarization-3.0",
                "pyannote/speaker-diarization"
            ]

            for model_name in models_to_try:
                try:
                    logger.info(f"尝试加载模型: {model_name}")
                    self.pipeline = Pipeline.from_pretrained(model_name, use_auth_token=True)
                    if self.pipeline is not None:
                        self.model_name = model_name
                        logger.info(f"✅ 成功加载模型: {model_name}")
                        return True
                except Exception as e:
                    logger.warning(f"❌ {model_name} 加载失败: {e}")
                    continue

            logger.error("所有pyannote模型都无法加载")
            return False

        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False

    def _merge_close_segments(self, segments, gap_threshold=1.0):
        """合并相近的片段"""
        if not segments:
            return []

        sorted_segments = sorted(segments, key=lambda x: x[0])
        merged = [sorted_segments[0]]

        for current_start, current_end in sorted_segments[1:]:
            last_start, last_end = merged[-1]

            if current_start - last_end <= gap_threshold:
                merged[-1] = (last_start, max(last_end, current_end))
            else:
                merged.append((current_start, current_end))

        return merged

    def process_audio(self, audio_path):
        """使用pyannote处理音频文件进行说话人分离"""
        try:
            logger.info(f"开始处理音频文件: {audio_path}")
            start_time = datetime.now()

            if not self.pipeline:
                raise RuntimeError("Pipeline未初始化")

            # 使用pyannote进行说话人分离
            logger.info("执行pyannote说话人分离...")
            diarization = self.pipeline(audio_path)

            # 分析结果
            speakers = list(diarization.labels())
            num_speakers = len(speakers)

            logger.info(f"检测到 {num_speakers} 个说话人: {speakers}")

            # 计算每个说话人的总时长
            speaker_durations = {}
            speaker_segments = {}

            for speaker in speakers:
                speaker_durations[speaker] = 0.0
                speaker_segments[speaker] = []

            for segment, _, speaker in diarization.itertracks(yield_label=True):
                duration = segment.end - segment.start
                speaker_durations[speaker] += duration
                speaker_segments[speaker].append((segment.start, segment.end))

            # 确定主要说话人（说话时间最长的）
            main_speaker = max(speaker_durations.items(), key=lambda x: x[1])[0]
            main_speaker_duration = speaker_durations[main_speaker]
            main_speaker_segments = speaker_segments[main_speaker]

            # 获取音频总时长
            audio_duration = max([segment.end for segment, _, _ in diarization.itertracks(yield_label=True)])

            # 合并相近的片段
            merged_segments = self._merge_close_segments(main_speaker_segments, gap_threshold=1.0)

            processing_time = (datetime.now() - start_time).total_seconds()

            # 创建所有片段的详细信息
            all_segments = []
            for segment, _, speaker in diarization.itertracks(yield_label=True):
                all_segments.append({
                    'start': float(segment.start),
                    'end': float(segment.end),
                    'speaker': speaker,
                    'is_main_speaker': bool(speaker == main_speaker)
                })

            result = {
                'audio_duration': float(audio_duration),
                'total_segments': len(all_segments),
                'num_speakers': num_speakers,
                'main_speaker': main_speaker,
                'speaker_durations': {k: float(v) for k, v in speaker_durations.items()},
                'main_speaker_segments': [(float(start), float(end)) for start, end in merged_segments],
                'all_segments': all_segments,
                'model_used': self.model_name,
                'processing_time': processing_time
            }

            logger.info(f"pyannote说话人分离完成，用时 {processing_time:.2f} 秒")
            logger.info(f"主要说话人: {main_speaker}, 时长: {main_speaker_duration:.2f}秒")
            logger.info(f"主要说话人片段数: {len(merged_segments)}")

            return result

        except Exception as e:
            logger.error(f"说话人分离失败: {e}")
            return None



def test_speaker_diarization():
    """测试pyannote说话人分离系统"""
    print("=" * 70)
    print("测试pyannote说话人分离系统")
    print("=" * 70)

    # 检查音频文件
    audio_file = "../data/机场地勤音频.WAV"
    if not os.path.exists(audio_file):
        print(f"错误: 音频文件不存在 - {audio_file}")
        return

    # 创建说话人分离系统
    diarization_system = SpeakerDiarizationSystem()

    # 初始化
    if not diarization_system.initialize():
        print("pyannote说话人分离系统初始化失败")
        return

    print(f"开始处理音频文件: {audio_file}")
    print(f"使用模型: {diarization_system.model_name}")

    # 处理音频
    result = diarization_system.process_audio(audio_file)

    if result:
        print(f"\n✅ pyannote说话人分离结果:")
        print(f"音频时长: {result['audio_duration']:.2f} 秒")
        print(f"处理时间: {result['processing_time']:.2f} 秒")
        print(f"检测到说话人数: {result['num_speakers']}")
        print(f"主要说话人: {result['main_speaker']}")
        print(f"使用模型: {result['model_used']}")

        print(f"\n说话人时长分布:")
        for speaker_id, duration in result['speaker_durations'].items():
            percentage = (duration / result['audio_duration']) * 100
            status = " (主要说话人)" if speaker_id == result['main_speaker'] else ""
            print(f"  说话人 {speaker_id}: {duration:.2f}秒 ({percentage:.1f}%){status}")

        print(f"\n主要说话人连续片段:")
        for i, (start, end) in enumerate(result['main_speaker_segments'], 1):
            duration = end - start
            print(f"  片段 {i}: {start:.1f}s - {end:.1f}s (时长: {duration:.1f}s)")

        # 保存结果
        output_file = f"pyannote_speaker_diarization_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        print(f"\n详细结果已保存到: {output_file}")

        return result
    else:
        print("❌ pyannote说话人分离失败")
        return None

if __name__ == "__main__":
    test_speaker_diarization()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机场地勤ASR系统主入口
可指定音频文件和输出路径
"""

import os
import sys
import argparse
import logging
import warnings
import json
from datetime import datetime
from pathlib import Path

# 抑制警告
warnings.filterwarnings("ignore")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入说话人分离系统
from speaker_diarization_system import SpeakerDiarizationSystem

# 导入Whisper
try:
    from faster_whisper import WhisperModel
    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False

class GroundCrewASRSystem:
    """机场地勤ASR系统"""

    def __init__(self, output_dir="GroundCrewASR", whisper_model="large-v2", fast_mode=False):
        self.whisper_model = None
        self.speaker_system = None
        self.output_dir = output_dir
        self.whisper_model_name = whisper_model
        self.fast_mode = fast_mode

        # 创建输出目录
        Path(self.output_dir).mkdir(exist_ok=True)
        logger.info(f"输出目录: {os.path.abspath(self.output_dir)}")
        if fast_mode:
            logger.info("🚀 启用快速模式")

    def initialize(self):
        """初始化系统组件"""
        try:
            # 1. 初始化说话人分离系统
            logger.info("初始化pyannote说话人分离系统...")
            self.speaker_system = SpeakerDiarizationSystem()
            if not self.speaker_system.initialize():
                raise RuntimeError("说话人分离系统初始化失败")

            # 2. 初始化Whisper模型
            logger.info(f"初始化Whisper {self.whisper_model_name}模型...")
            if not WHISPER_AVAILABLE:
                raise ImportError("Whisper不可用")

            # 检查本地模型是否存在
            model_path = os.path.expanduser(f"~/.cache/huggingface/hub/models--Systran--faster-whisper-{self.whisper_model_name}")
            if os.path.exists(model_path):
                logger.info(f"✓ 发现本地{self.whisper_model_name}模型: {model_path}")
                try:
                    # 尝试使用本地路径加载
                    self.whisper_model = WhisperModel(
                        model_path,
                        device="cpu",
                        compute_type="int8",
                        cpu_threads=4
                    )
                    logger.info(f"✅ 使用本地{self.whisper_model_name}模型加载成功")
                except Exception as e:
                    logger.warning(f"⚠ 本地模型加载失败: {e}")
                    logger.info("尝试重新下载模型...")
                    # 如果本地模型损坏，重新下载
                    self.whisper_model = WhisperModel(
                        self.whisper_model_name,
                        device="cpu",
                        compute_type="int8",
                        cpu_threads=4
                    )
                    logger.info(f"✅ 重新下载{self.whisper_model_name}模型成功")
            else:
                logger.info(f"本地{self.whisper_model_name}模型不存在，开始下载...")
                self.whisper_model = WhisperModel(
                    self.whisper_model_name,
                    device="cpu",
                    compute_type="int8",
                    cpu_threads=4
                )
                logger.info(f"✅ 下载{self.whisper_model_name}模型成功")

            logger.info("✅ 系统初始化成功")
            return True

        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            return False

    def extract_main_speaker_audio(self, audio_path, main_speaker_segments):
        """提取主要说话人的音频片段"""
        try:
            from audio_loader import load_audio

            # 加载完整音频
            wav = load_audio(audio_path, sr=16000)

            # 提取主要说话人片段
            main_speaker_audio_segments = []

            for start_time, end_time in main_speaker_segments:
                start_sample = int(start_time * 16000)
                end_sample = int(end_time * 16000)

                # 确保索引在有效范围内
                start_sample = max(0, start_sample)
                end_sample = min(len(wav), end_sample)

                if end_sample > start_sample:
                    segment_audio = wav[start_sample:end_sample]
                    main_speaker_audio_segments.append({
                        'audio': segment_audio,
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration': end_time - start_time
                    })

            logger.info(f"提取了 {len(main_speaker_audio_segments)} 个主要说话人音频片段")
            return main_speaker_audio_segments

        except Exception as e:
            logger.error(f"音频片段提取失败: {e}")
            return []

    def transcribe_segments(self, audio_segments):
        """使用Whisper Large-v2转录音频片段"""
        try:
            logger.info("开始转录音频片段...")

            transcribed_segments = []

            for i, segment in enumerate(audio_segments):
                logger.info(f"转录片段 {i+1}/{len(audio_segments)}: {segment['start_time']:.1f}s-{segment['end_time']:.1f}s")

                # 保存临时音频文件
                temp_audio_path = f"temp_segment_{i}.wav"

                try:
                    import soundfile as sf
                    sf.write(temp_audio_path, segment['audio'], 16000)

                    # 根据模式选择转录参数
                    if self.fast_mode:
                        # 快速模式：优先速度
                        segments, info = self.whisper_model.transcribe(
                            temp_audio_path,
                            language="zh",
                            beam_size=1,
                            temperature=0.0,
                            word_timestamps=False,  # 关闭词级时间戳
                            vad_filter=True,
                            vad_parameters=dict(
                                min_silence_duration_ms=1000,  # 更大的静音阈值
                                speech_pad_ms=100,
                                max_speech_duration_s=30
                            ),
                            condition_on_previous_text=False,
                            no_speech_threshold=0.8  # 更高的无语音阈值
                        )
                    else:
                        # 标准模式：平衡速度和准确性
                        segments, info = self.whisper_model.transcribe(
                            temp_audio_path,
                            language="zh",
                            beam_size=1,
                            temperature=0.0,
                            word_timestamps=True,
                            vad_filter=True,
                            vad_parameters=dict(
                                min_silence_duration_ms=500,
                                speech_pad_ms=200,
                                max_speech_duration_s=30
                            ),
                            condition_on_previous_text=False,
                            compression_ratio_threshold=2.4,
                            log_prob_threshold=-1.0,
                            no_speech_threshold=0.6
                        )

                    # 收集转录结果
                    segment_text = ""
                    segment_words = []

                    for whisper_segment in segments:
                        segment_text += whisper_segment.text

                        if hasattr(whisper_segment, 'words') and whisper_segment.words:
                            for word in whisper_segment.words:
                                segment_words.append({
                                    'word': word.word.strip(),
                                    'start': segment['start_time'] + word.start,
                                    'end': segment['start_time'] + word.end,
                                    'probability': word.probability
                                })

                    transcribed_segment = {
                        'start_time': segment['start_time'],
                        'end_time': segment['end_time'],
                        'duration': segment['duration'],
                        'text': segment_text.strip(),
                        'language': info.language,
                        'language_probability': info.language_probability,
                        'words': segment_words
                    }

                    transcribed_segments.append(transcribed_segment)
                    logger.info(f"片段转录完成: {segment_text.strip()}")

                except Exception as e:
                    logger.error(f"片段 {i} 转录失败: {e}")
                    transcribed_segments.append({
                        'start_time': segment['start_time'],
                        'end_time': segment['end_time'],
                        'duration': segment['duration'],
                        'text': '',
                        'error': str(e)
                    })

                finally:
                    # 清理临时文件
                    if os.path.exists(temp_audio_path):
                        os.remove(temp_audio_path)

            logger.info(f"转录完成，共 {len(transcribed_segments)} 个片段")
            return transcribed_segments

        except Exception as e:
            logger.error(f"转录失败: {e}")
            return []

    def process_audio(self, audio_path):
        """处理音频文件"""
        try:
            logger.info(f"开始处理音频文件: {audio_path}")
            start_time = datetime.now()

            # 1. 说话人分离
            logger.info("步骤1: 说话人分离...")
            speaker_result = self.speaker_system.process_audio(audio_path)
            if not speaker_result:
                raise RuntimeError("说话人分离失败")

            # 2. 提取主要说话人音频
            logger.info("步骤2: 提取主要说话人音频...")
            main_speaker_segments = speaker_result['main_speaker_segments']
            audio_segments = self.extract_main_speaker_audio(audio_path, main_speaker_segments)

            if not audio_segments:
                raise RuntimeError("没有提取到主要说话人音频")

            # 3. 转录所有说话人音频
            logger.info("步骤3: 转录所有说话人音频...")
            all_speaker_transcriptions = self.transcribe_all_speakers(audio_path, speaker_result)

            # 4. 整理结果
            processing_time = (datetime.now() - start_time).total_seconds()

            # 计算统计信息
            total_main_speaker_duration = sum([seg['duration'] for seg in audio_segments])
            main_speaker_text = all_speaker_transcriptions.get(speaker_result['main_speaker'], '')
            total_words = len(main_speaker_text.split()) if main_speaker_text else 0

            result = {
                'system_info': {
                    'version': '机场地勤ASR系统 v2.0 (Large-v2)',
                    'whisper_model': self.whisper_model_name,
                    'speaker_model': speaker_result.get('model_used', 'pyannote'),
                    'processing_time': processing_time,
                    'timestamp': datetime.now().isoformat()
                },
                'audio_info': {
                    'file_path': audio_path,
                    'total_duration': speaker_result['audio_duration'],
                    'main_speaker_duration': total_main_speaker_duration,
                    'main_speaker_percentage': (total_main_speaker_duration / speaker_result['audio_duration']) * 100
                },
                'speaker_diarization': {
                    'num_speakers': speaker_result['num_speakers'],
                    'main_speaker': speaker_result['main_speaker'],
                    'speaker_durations': speaker_result['speaker_durations'],
                    'main_speaker_segments': main_speaker_segments,
                    'all_segments': speaker_result['all_segments']
                },
                'transcription': {
                    'main_speaker_text': main_speaker_text,
                    'total_words': total_words,
                    'all_speaker_transcriptions': all_speaker_transcriptions
                }
            }

            logger.info(f"处理完成，用时 {processing_time:.2f} 秒")
            return result

        except Exception as e:
            logger.error(f"音频处理失败: {e}")
            return None

    def transcribe_all_speakers(self, audio_path, speaker_result):
        """为所有说话人生成转录"""
        try:
            logger.info("开始为所有说话人生成转录...")

            all_speaker_transcriptions = {}

            # 为每个说话人提取音频片段并转录
            for speaker_id in speaker_result['speaker_durations'].keys():
                logger.info(f"处理说话人: {speaker_id}")

                # 获取该说话人的所有片段
                speaker_segments = []
                for segment in speaker_result['all_segments']:
                    if segment['speaker'] == speaker_id:
                        speaker_segments.append((segment['start'], segment['end']))

                if not speaker_segments:
                    all_speaker_transcriptions[speaker_id] = ""
                    continue

                # 合并相近的片段
                merged_segments = self._merge_close_segments(speaker_segments, gap_threshold=1.0)

                # 提取音频并转录
                audio_segments = self.extract_main_speaker_audio(audio_path, merged_segments)
                if audio_segments:
                    transcribed_segments = self.transcribe_segments(audio_segments)
                    # 合并转录文本
                    full_text = ' '.join([seg['text'] for seg in transcribed_segments if seg.get('text')])
                    all_speaker_transcriptions[speaker_id] = full_text.strip()
                else:
                    all_speaker_transcriptions[speaker_id] = ""

            return all_speaker_transcriptions

        except Exception as e:
            logger.error(f"所有说话人转录失败: {e}")
            return {}

    def _merge_close_segments(self, segments, gap_threshold=1.0):
        """合并相近的片段"""
        if not segments:
            return []

        sorted_segments = sorted(segments, key=lambda x: x[0])
        merged = [sorted_segments[0]]

        for current_start, current_end in sorted_segments[1:]:
            last_start, last_end = merged[-1]

            if current_start - last_end <= gap_threshold:
                merged[-1] = (last_start, max(last_end, current_end))
            else:
                merged.append((current_start, current_end))

        return merged

    def save_results(self, result, audio_filename, all_speaker_transcriptions):
        """保存结果到指定目录"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        base_name = Path(audio_filename).stem

        # 1. 保存简化的说话人转录信息 (纯文本，包含时间信息)
        speaker_file = os.path.join(self.output_dir, f"{base_name}_speakers_{timestamp}.txt")
        with open(speaker_file, 'w', encoding='utf-8') as f:
            # 写入文件头信息
            f.write(f"音频文件: {audio_filename}\n")
            f.write(f"处理时间: {result['system_info']['timestamp']}\n")
            f.write(f"音频时长: {result['audio_info']['total_duration']:.2f}秒\n")
            f.write(f"检测说话人数: {result['speaker_diarization']['num_speakers']}\n")
            f.write("=" * 60 + "\n\n")

            # 获取说话人时长信息
            speaker_durations = result['speaker_diarization']['speaker_durations']
            total_duration = result['audio_info']['total_duration']

            for speaker_id in sorted(all_speaker_transcriptions.keys()):
                transcription = all_speaker_transcriptions[speaker_id]
                duration = speaker_durations.get(speaker_id, 0)
                percentage = (duration / total_duration) * 100 if total_duration > 0 else 0

                # 写入说话人信息（包含时间）
                f.write(f"{speaker_id} (时长: {duration:.1f}秒, 占比: {percentage:.1f}%):\n")
                if transcription:
                    f.write(f"  {transcription}\n")
                else:
                    f.write("  [无转录内容]\n")
                f.write("\n")

        # 2. 保存详细结果 (JSON)
        detail_file = os.path.join(self.output_dir, f"{base_name}_detail_{timestamp}.json")
        result['all_speaker_transcriptions'] = all_speaker_transcriptions
        with open(detail_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        return speaker_file, detail_file

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='机场地勤ASR系统')
    parser.add_argument('audio_file', help='音频文件路径')
    parser.add_argument('--output-dir', '-o', default='GroundCrewASR',
                       help='输出目录 (默认: GroundCrewASR)')
    parser.add_argument('--whisper-model', '-m', default='large-v2',
                       choices=['large-v2', 'large-v3'],
                       help='Whisper模型版本 (默认: large-v2)')
    parser.add_argument('--fast-mode', '-f', action='store_true',
                       help='快速模式 (牺牲少量准确性换取更快速度)')

    args = parser.parse_args()

    # 检查音频文件
    if not os.path.exists(args.audio_file):
        print(f"错误: 音频文件不存在 - {args.audio_file}")
        return 1

    print("=" * 80)
    mode_text = " (快速模式)" if args.fast_mode else ""
    print(f"机场地勤ASR系统 (pyannote + Whisper {args.whisper_model.upper()}){mode_text}")
    print("=" * 80)
    print(f"音频文件: {args.audio_file}")
    print(f"输出目录: {args.output_dir}")
    print(f"Whisper模型: {args.whisper_model}")
    print(f"运行模式: {'快速模式' if args.fast_mode else '标准模式'}")
    print("=" * 80)

    # 创建系统
    asr_system = GroundCrewASRSystem(
        output_dir=args.output_dir,
        whisper_model=args.whisper_model,
        fast_mode=args.fast_mode
    )

    # 初始化
    if not asr_system.initialize():
        print("系统初始化失败")
        return 1

    # 处理音频
    result = asr_system.process_audio(args.audio_file)

    if result:
        # 保存结果
        all_speaker_transcriptions = result['transcription']['all_speaker_transcriptions']
        speaker_file, detail_file = asr_system.save_results(result, args.audio_file, all_speaker_transcriptions)

        # 显示结果
        print(f"\n✅ 处理完成!")
        print(f"音频时长: {result['audio_info']['total_duration']:.2f} 秒")
        print(f"检测到说话人数: {result['speaker_diarization']['num_speakers']}")
        print(f"处理时间: {result['system_info']['processing_time']:.2f} 秒")

        print(f"\n📁 输出文件:")
        print(f"说话人转录: {speaker_file}")
        print(f"详细结果: {detail_file}")

        print(f"\n🎤 说话人转录结果:")
        for speaker_id in sorted(all_speaker_transcriptions.keys()):
            transcription = all_speaker_transcriptions[speaker_id]
            duration = result['speaker_diarization']['speaker_durations'][speaker_id]
            percentage = (duration / result['audio_info']['total_duration']) * 100
            if transcription:
                print(f"  {speaker_id} ({duration:.1f}s, {percentage:.1f}%): {transcription}")
            else:
                print(f"  {speaker_id} ({duration:.1f}s, {percentage:.1f}%): [无转录内容]")

        return 0
    else:
        print("❌ 处理失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())

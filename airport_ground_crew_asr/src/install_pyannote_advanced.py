#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多方案安装pyannote.audio
"""

import subprocess
import sys
import os
import time

def run_command(cmd, timeout=600):
    """运行命令并返回结果"""
    try:
        print(f"执行: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        print(f"命令超时 ({timeout}秒)")
        return False, "", "超时"
    except Exception as e:
        print(f"命令执行失败: {e}")
        return False, "", str(e)

def method_1_direct_install():
    """方法1: 直接安装"""
    print("\n" + "="*60)
    print("方法1: 直接安装pyannote.audio")
    print("="*60)
    
    success, stdout, stderr = run_command([
        sys.executable, "-m", "pip", "install", "pyannote.audio"
    ])
    
    if success:
        print("✓ 方法1成功")
        return True
    else:
        print("✗ 方法1失败")
        print(f"错误: {stderr}")
        return False

def method_2_upgrade_pip():
    """方法2: 升级pip后安装"""
    print("\n" + "="*60)
    print("方法2: 升级pip后安装")
    print("="*60)
    
    # 升级pip
    print("升级pip...")
    success, _, _ = run_command([
        sys.executable, "-m", "pip", "install", "--upgrade", "pip"
    ])
    
    if not success:
        print("pip升级失败")
        return False
    
    # 安装pyannote.audio
    success, stdout, stderr = run_command([
        sys.executable, "-m", "pip", "install", "pyannote.audio"
    ])
    
    if success:
        print("✓ 方法2成功")
        return True
    else:
        print("✗ 方法2失败")
        print(f"错误: {stderr}")
        return False

def method_3_install_dependencies():
    """方法3: 先安装依赖再安装"""
    print("\n" + "="*60)
    print("方法3: 先安装依赖")
    print("="*60)
    
    # 安装编译依赖
    dependencies = [
        "setuptools",
        "wheel", 
        "Cython",
        "numpy",
        "scipy",
        "scikit-learn",
        "torch",
        "torchaudio",
        "librosa",
        "soundfile",
        "matplotlib",
        "seaborn",
        "pandas",
        "pyyaml",
        "omegaconf",
        "hydra-core",
        "pytorch-lightning",
        "torchmetrics",
        "asteroid-filterbanks",
        "speechbrain",
        "huggingface_hub"
    ]
    
    for dep in dependencies:
        print(f"安装 {dep}...")
        success, _, stderr = run_command([
            sys.executable, "-m", "pip", "install", dep
        ], timeout=300)
        
        if success:
            print(f"✓ {dep}")
        else:
            print(f"⚠️ {dep} 安装失败: {stderr[:100]}...")
    
    # 现在安装pyannote.audio
    print("\n安装pyannote.audio...")
    success, stdout, stderr = run_command([
        sys.executable, "-m", "pip", "install", "pyannote.audio"
    ])
    
    if success:
        print("✓ 方法3成功")
        return True
    else:
        print("✗ 方法3失败")
        print(f"错误: {stderr}")
        return False

def method_4_conda_install():
    """方法4: 使用conda安装"""
    print("\n" + "="*60)
    print("方法4: 使用conda安装")
    print("="*60)
    
    # 检查conda是否可用
    success, _, _ = run_command(["conda", "--version"])
    if not success:
        print("conda不可用，跳过此方法")
        return False
    
    # 使用conda安装
    success, stdout, stderr = run_command([
        "conda", "install", "-c", "conda-forge", "pyannote-audio", "-y"
    ])
    
    if success:
        print("✓ 方法4成功")
        return True
    else:
        print("✗ 方法4失败")
        print(f"错误: {stderr}")
        return False

def method_5_git_install():
    """方法5: 从GitHub源码安装"""
    print("\n" + "="*60)
    print("方法5: 从GitHub源码安装")
    print("="*60)
    
    # 克隆仓库
    print("克隆pyannote-audio仓库...")
    success, _, stderr = run_command([
        "git", "clone", "https://github.com/pyannote/pyannote-audio.git"
    ])
    
    if not success:
        print(f"克隆失败: {stderr}")
        return False
    
    # 进入目录并安装
    original_dir = os.getcwd()
    try:
        os.chdir("pyannote-audio")
        
        # 安装
        success, stdout, stderr = run_command([
            sys.executable, "-m", "pip", "install", "-e", "."
        ])
        
        if success:
            print("✓ 方法5成功")
            return True
        else:
            print("✗ 方法5失败")
            print(f"错误: {stderr}")
            return False
    finally:
        os.chdir(original_dir)

def method_6_alternative_package():
    """方法6: 安装替代包"""
    print("\n" + "="*60)
    print("方法6: 安装替代的说话人分离包")
    print("="*60)
    
    alternatives = [
        "resemblyzer",  # 简单的说话人分离
        "speechbrain",  # SpeechBrain包含说话人分离
        "pyannote.core",  # 核心功能
    ]
    
    success_count = 0
    for package in alternatives:
        print(f"安装 {package}...")
        success, _, stderr = run_command([
            sys.executable, "-m", "pip", "install", package
        ])
        
        if success:
            print(f"✓ {package}")
            success_count += 1
        else:
            print(f"✗ {package}: {stderr[:100]}...")
    
    return success_count > 0

def test_installation():
    """测试安装结果"""
    print("\n" + "="*60)
    print("测试安装结果")
    print("="*60)
    
    # 测试pyannote.audio
    try:
        from pyannote.audio import Pipeline
        print("✓ pyannote.audio 导入成功")
        return True
    except ImportError as e:
        print(f"✗ pyannote.audio 导入失败: {e}")
    
    # 测试替代方案
    alternatives_available = []
    
    try:
        import resemblyzer
        print("✓ resemblyzer 可用")
        alternatives_available.append("resemblyzer")
    except ImportError:
        print("✗ resemblyzer 不可用")
    
    try:
        import speechbrain
        print("✓ speechbrain 可用")
        alternatives_available.append("speechbrain")
    except ImportError:
        print("✗ speechbrain 不可用")
    
    try:
        from pyannote.core import Segment, Timeline
        print("✓ pyannote.core 可用")
        alternatives_available.append("pyannote.core")
    except ImportError:
        print("✗ pyannote.core 不可用")
    
    if alternatives_available:
        print(f"\n可用的替代方案: {', '.join(alternatives_available)}")
        return True
    
    return False

def main():
    """主函数"""
    print("=" * 70)
    print("多方案安装pyannote.audio说话人分离工具")
    print("=" * 70)
    
    methods = [
        ("方法1: 直接安装", method_1_direct_install),
        ("方法2: 升级pip后安装", method_2_upgrade_pip),
        ("方法3: 先安装依赖", method_3_install_dependencies),
        ("方法4: 使用conda", method_4_conda_install),
        ("方法5: 从源码安装", method_5_git_install),
        ("方法6: 安装替代包", method_6_alternative_package),
    ]
    
    for method_name, method_func in methods:
        print(f"\n尝试 {method_name}...")
        
        try:
            if method_func():
                print(f"✓ {method_name} 成功!")
                break
        except Exception as e:
            print(f"✗ {method_name} 出现异常: {e}")
        
        print(f"✗ {method_name} 失败，尝试下一个方法...")
    
    # 测试最终结果
    success = test_installation()
    
    if success:
        print("\n" + "="*70)
        print("🎉 安装成功！可以使用说话人分离功能了")
        print("="*70)
    else:
        print("\n" + "="*70)
        print("⚠️ 所有方法都失败了，但我们可以尝试其他解决方案")
        print("="*70)

if __name__ == "__main__":
    main()

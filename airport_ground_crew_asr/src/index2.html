<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>机场地勤ASR系统架构流程图</title>
    <style>
      body {
        font-family: "Arial", "Microsoft YaHei", sans-serif;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background-color: #f5f5f5;
        margin: 0;
        padding: 20px;
        box-sizing: border-box;
      }
      svg {
        background-color: #ffffff;
        border-radius: 15px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        max-width: 100%;
        height: auto;
      }

      /* Color definitions */
      :root {
        --input-output-bg: #e8f6f8; /* 浅蓝绿色 */
        --process-bg: #e3f2fd; /* 浅蓝色 */
        --line-color: #78909c; /* 柔和的灰色调 */
        --text-color-dark: #333;
        --text-color-medium: #555;
        --border-color: #ccc;
        --core-tech-highlight: #1e88e5; /* 醒目的蓝色 */
      }

      /* SVG specific styles */
      .module-rect {
        stroke: var(--border-color);
        stroke-width: 1;
        rx: 10;
        ry: 10;
      }
      .main-controller-border {
        stroke: #607d8b; /* 用于主边框的深灰色 */
        stroke-width: 2;
        stroke-dasharray: 8 4; /* 虚线 */
        fill: none;
        rx: 15;
        ry: 15;
      }
      .module-title {
        font-size: 18px;
        font-weight: bold;
        fill: var(--text-color-dark);
        text-anchor: middle; /* 模块标题居中 */
      }
      .module-subtitle {
        font-size: 14px;
        font-weight: bold;
        fill: var(--core-tech-highlight);
        text-anchor: middle; /* 副标题居中 */
      }
      .module-detail-left {
        /* 用于左对齐的详细描述 */
        font-size: 12px;
        fill: var(--text-color-medium);
        text-anchor: start; /* 左对齐 */
      }
      .arrow-line {
        stroke: var(--line-color);
        stroke-width: 2;
        marker-end: url(#arrowhead);
        fill: none;
      }
      .arrow-label {
        font-size: 12px;
        fill: var(--text-color-medium);
        text-anchor: middle;
      }
      .main-title {
        font-size: 28px;
        font-weight: bold;
        fill: #2c3e50;
        text-anchor: middle;
      }
      .whisperx-label {
        font-size: 20px;
        font-weight: bold;
        fill: #333;
        text-anchor: middle;
      }
    </style>
  </head>
  <body>
    <svg width="1200" height="680" viewBox="0 0 1200 680">
      <defs>
        <marker
          id="arrowhead"
          markerWidth="10"
          markerHeight="7"
          refX="0"
          refY="3.5"
          orient="auto"
        >
          <polygon points="0 0, 10 3.5, 0 7" fill="var(--line-color)" />
        </marker>
      </defs>

      <!-- 主标题 -->
      <text x="600" y="50" class="main-title">机场地勤ASR系统架构流程图</text>

      <!-- WhisperX 主控流程框架容器 (虚线边框) -->
      <!-- 调整尺寸和位置，使其包含所有内部模块，并与外部模块保持适当间距 -->
      <rect
        x="200"
        y="90"
        width="800"
        height="480"
        class="main-controller-border"
      />
      <text x="600" y="130" class="whisperx-label">WhisperX 主控流程框架</text>

      <!-- 1. 音频输入 (Input Stage) -->
      <!-- 调整尺寸和位置，文本左对齐 -->
      <rect
        x="50"
        y="260"
        width="150"
        height="150"
        fill="var(--input-output-bg)"
        class="module-rect"
      />
      <text x="125" y="300" class="module-title">音频输入</text>
      <text x="65" y="330" class="module-detail-left">
        多种格式 (MP3, WAV, M4A)
      </text>
      <text x="65" y="345" class="module-detail-left">机场地勤通信录音</text>
      <text x="65" y="360" class="module-detail-left">可能包含多个说话人</text>

      <!-- 2. 预处理阶段 (Preprocessing Stage) -->
      <!-- 调整尺寸和位置，文本左对齐 -->
      <rect
        x="230"
        y="170"
        width="200"
        height="180"
        fill="var(--process-bg)"
        class="module-rect"
      />
      <text x="330" y="205" class="module-title">预处理阶段</text>
      <text x="330" y="235" class="module-subtitle">pyannote.audio</text>
      <text x="245" y="265" class="module-detail-left">语音活动检测 (VAD)</text>
      <text x="245" y="280" class="module-detail-left">
        说话人分离 (Speaker Diarization)
      </text>
      <text x="245" y="295" class="module-detail-left">
        输出: 带时间戳和说话人标签
      </text>

      <!-- 3. 语音片段处理 (Audio Segment Processing) -->
      <!-- 调整尺寸和位置，文本左对齐 -->
      <rect
        x="230"
        y="390"
        width="200"
        height="120"
        fill="var(--process-bg)"
        class="module-rect"
      />
      <text x="330" y="425" class="module-title">语音片段处理</text>
      <text x="245" y="455" class="module-detail-left">
        根据 Diarization 结果切割
      </text>
      <text x="245" y="470" class="module-detail-left">
        片段标注 (开始/结束时间, 说话人ID)
      </text>

      <!-- 4. 语音识别阶段 (Speech Recognition Stage) -->
      <!-- 调整尺寸和位置，文本左对齐 -->
      <rect
        x="480"
        y="280"
        width="200"
        height="180"
        fill="var(--process-bg)"
        class="module-rect"
      />
      <text x="580" y="315" class="module-title">语音识别阶段</text>
      <text x="580" y="345" class="module-subtitle">OpenAI Whisper</text>
      <text x="495" y="365" class="module-detail-left">(large-v3 模型)</text>
      <text x="495" y="390" class="module-detail-left">
        对每个语音片段单独ASR
      </text>
      <text x="495" y="405" class="module-detail-left">
        生成带时间戳的转录文本
      </text>

      <!-- 5. 结果整合阶段 (Result Integration Stage) -->
      <!-- 调整尺寸和位置，文本左对齐 -->
      <rect
        x="730"
        y="310"
        width="200"
        height="120"
        fill="var(--process-bg)"
        class="module-rect"
      />
      <text x="830" y="345" class="module-title">结果整合阶段</text>
      <text x="745" y="375" class="module-detail-left">
        整合说话人信息、转录文本
      </text>
      <text x="745" y="390" class="module-detail-left">标准化输出格式化</text>

      <!-- 6. 输出阶段 (Output Stage) -->
      <!-- **重点调整**: 增加宽度，并确保文本左对齐，同时调整X位置使其适配新宽度和整体布局 -->
      <rect
        x="1010"
        y="260"
        width="150"
        height="150"
        fill="var(--input-output-bg)"
        class="module-rect"
      />
      <text x="1085" y="300" class="module-title">输出阶段</text>
      <text x="1025" y="330" class="module-detail-left">SRT 字幕</text>
      <text x="1025" y="345" class="module-detail-left">JSON 数据</text>
      <text x="1025" y="360" class="module-detail-left">VTT 格式</text>

      <!-- 连接线 -->
      <!-- 重新校准所有连接线的坐标和标签位置 -->
      <!-- 音频输入 -> 预处理阶段 -->
      <line x1="200" y1="335" x2="230" y2="260" class="arrow-line" />
      <text x="215" y="295" class="arrow-label">原始音频</text>

      <!-- 预处理阶段 -> 语音片段处理 -->
      <line x1="330" y1="350" x2="330" y2="390" class="arrow-line" />
      <text x="330" y="370" class="arrow-label">Diarization结果</text>

      <!-- 语音片段处理 -> 语音识别阶段 -->
      <line x1="430" y1="450" x2="480" y2="370" class="arrow-line" />
      <text x="455" y="410" class="arrow-label">语音片段</text>

      <!-- 语音识别阶段 -> 结果整合阶段 -->
      <line x1="680" y1="370" x2="730" y2="370" class="arrow-line" />
      <text x="705" y="360" class="arrow-label">转录文本(含时间戳)</text>

      <!-- 结果整合阶段 -> 输出阶段 -->
      <line x1="930" y1="370" x2="1010" y2="370" class="arrow-line" />
      <text x="970" y="360" class="arrow-label">结构化转录数据</text>
    </svg>
  </body>
</html>

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Large-v2模型加载
"""

import os
import logging
from faster_whisper import WhisperModel

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_large_v2_model():
    """测试Large-v2模型加载"""
    try:
        print("正在测试Large-v2模型加载...")
        
        # 尝试不同的加载方式
        print("方法1: 直接加载large-v2")
        try:
            model = WhisperModel("large-v2", device="cpu", compute_type="int8")
            print("✓ 方法1成功: large-v2模型加载成功")
            
            # 测试转录
            audio_file = "机场地勤音频.WAV"
            if os.path.exists(audio_file):
                print("测试转录...")
                segments, info = model.transcribe(audio_file, language="zh")
                print(f"✓ 转录测试成功，检测语言: {info.language}")
                
                # 显示前几个片段
                for i, segment in enumerate(segments):
                    if i >= 3:  # 只显示前3个片段
                        break
                    print(f"  {i+1}. [{segment.start:.1f}s-{segment.end:.1f}s] {segment.text}")
            
            del model
            return True
            
        except Exception as e:
            print(f"✗ 方法1失败: {e}")
        
        print("\n方法2: 使用本地路径")
        try:
            model_path = os.path.expanduser("~/.cache/huggingface/hub/models--Systran--faster-whisper-large-v2")
            model = WhisperModel(model_path, device="cpu", compute_type="int8")
            print("✓ 方法2成功: 本地路径加载成功")
            del model
            return True
            
        except Exception as e:
            print(f"✗ 方法2失败: {e}")
        
        print("\n方法3: 重新下载模型")
        try:
            # 删除可能损坏的模型文件
            import shutil
            model_dir = os.path.expanduser("~/.cache/huggingface/hub/models--Systran--faster-whisper-large-v2")
            if os.path.exists(model_dir):
                print("删除现有模型文件...")
                shutil.rmtree(model_dir)
            
            print("重新下载large-v2模型...")
            model = WhisperModel("large-v2", device="cpu", compute_type="int8", download_root=os.path.expanduser("~/.cache/huggingface"))
            print("✓ 方法3成功: 重新下载并加载成功")
            del model
            return True
            
        except Exception as e:
            print(f"✗ 方法3失败: {e}")
        
        return False
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def test_alternative_models():
    """测试其他可用的模型"""
    models_to_test = ["medium", "small", "base"]
    
    for model_name in models_to_test:
        try:
            print(f"\n测试 {model_name} 模型...")
            model = WhisperModel(model_name, device="cpu", compute_type="int8")
            print(f"✓ {model_name} 模型加载成功")
            
            # 简单测试
            audio_file = "机场地勤音频.WAV"
            if os.path.exists(audio_file):
                segments, info = model.transcribe(audio_file, language="zh")
                segment_count = sum(1 for _ in segments)
                print(f"  转录片段数: {segment_count}")
            
            del model
            return model_name
            
        except Exception as e:
            print(f"✗ {model_name} 模型失败: {e}")
    
    return None

if __name__ == "__main__":
    print("=" * 60)
    print("Whisper Large-v2 模型测试")
    print("=" * 60)
    
    # 测试large-v2
    success = test_large_v2_model()
    
    if not success:
        print("\n" + "=" * 60)
        print("Large-v2失败，测试其他模型...")
        print("=" * 60)
        
        working_model = test_alternative_models()
        
        if working_model:
            print(f"\n建议使用 {working_model} 模型作为替代方案")
        else:
            print("\n所有模型测试都失败了")
    
    print("\n测试完成")

#!/usr/bin/env python3
"""
说话人角色分析工具
基于转录内容自动识别地勤人员和乘客
"""

import json
import re
import argparse
from pathlib import Path

# 地勤人员常用词汇
GROUND_CREW_KEYWORDS = [
    # 服务用语
    "请", "您", "先生", "女士", "麻烦", "谢谢", "不客气", "没关系",
    # 座位相关
    "座位", "位置", "坐", "排", "号", "窗口", "过道", "中间",
    # 登机相关
    "登机", "登机牌", "身份证", "护照", "票", "订单", "航班",
    # 行李相关
    "行李", "包", "箱子", "托运", "随身",
    # 安全检查
    "安检", "检查", "安全", "禁止", "不能", "不可以",
    # 指引用语
    "这边", "那边", "前面", "后面", "左边", "右边", "跟我来",
    # 工作术语
    "打出来", "系统", "确认", "核实", "办理", "手续"
]

# 乘客常用词汇
PASSENGER_KEYWORDS = [
    # 询问用语
    "在哪", "怎么", "什么时候", "多少", "可以吗", "行吗",
    # 个人信息
    "我的", "我们", "我", "家人", "孩子", "老人",
    # 需求表达
    "想要", "需要", "希望", "能不能", "可不可以",
    # 问题反馈
    "找不到", "不知道", "不明白", "有问题", "出错了"
]

def analyze_speaker_role(text):
    """
    基于文本内容分析说话人角色
    返回: ('ground_crew', confidence) 或 ('passenger', confidence)
    """
    if not text or len(text.strip()) < 3:
        return 'unknown', 0.0
    
    text = text.lower()
    
    # 计算关键词匹配分数
    ground_crew_score = 0
    passenger_score = 0
    
    for keyword in GROUND_CREW_KEYWORDS:
        if keyword in text:
            ground_crew_score += 1
    
    for keyword in PASSENGER_KEYWORDS:
        if keyword in text:
            passenger_score += 1
    
    # 特殊规则
    # 1. 包含"请"、"您"等敬语的通常是地勤
    if any(word in text for word in ["请", "您", "麻烦您"]):
        ground_crew_score += 2
    
    # 2. 包含"我的"、"我们"等第一人称的通常是乘客
    if any(word in text for word in ["我的", "我们的", "我想"]):
        passenger_score += 1
    
    # 3. 包含工作术语的是地勤
    work_terms = ["打出来", "身份证", "订单", "系统", "确认"]
    if any(term in text for term in work_terms):
        ground_crew_score += 2
    
    # 4. 短语句通常是回应，可能是乘客
    if len(text) < 10:
        passenger_score += 0.5
    
    # 计算置信度
    total_score = ground_crew_score + passenger_score
    if total_score == 0:
        return 'unknown', 0.0
    
    if ground_crew_score > passenger_score:
        confidence = ground_crew_score / total_score
        return 'ground_crew', confidence
    else:
        confidence = passenger_score / total_score
        return 'passenger', confidence

def analyze_json_result(json_file):
    """分析JSON结果文件"""
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    transcriptions = data.get('transcription', {}).get('all_speaker_transcriptions', {})
    speaker_durations = data.get('speaker_diarization', {}).get('speaker_durations', {})
    
    analysis_result = {}
    
    print("=" * 60)
    print("说话人角色分析结果")
    print("=" * 60)
    
    for speaker_id, text in transcriptions.items():
        role, confidence = analyze_speaker_role(text)
        duration = speaker_durations.get(speaker_id, 0)
        
        analysis_result[speaker_id] = {
            'role': role,
            'confidence': confidence,
            'duration': duration,
            'text': text
        }
        
        # 显示分析结果
        role_cn = {
            'ground_crew': '地勤人员',
            'passenger': '乘客',
            'unknown': '未知'
        }.get(role, '未知')
        
        print(f"{speaker_id}: {role_cn} (置信度: {confidence:.2f}, 时长: {duration:.1f}s)")
        print(f"  内容: {text[:100]}{'...' if len(text) > 100 else ''}")
        print()
    
    return analysis_result

def generate_classified_output(analysis_result, output_file):
    """生成分类后的输出文件"""
    ground_crew_content = []
    passenger_content = []
    unknown_content = []
    
    for speaker_id, info in analysis_result.items():
        role = info['role']
        text = info['text']
        duration = info['duration']
        confidence = info['confidence']
        
        content_line = f"{speaker_id} (时长: {duration:.1f}s, 置信度: {confidence:.2f}): {text}"
        
        if role == 'ground_crew':
            ground_crew_content.append(content_line)
        elif role == 'passenger':
            passenger_content.append(content_line)
        else:
            unknown_content.append(content_line)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("机场地勤ASR系统 - 角色分类结果\n")
        f.write("=" * 60 + "\n\n")
        
        f.write("🧑‍💼 地勤人员:\n")
        f.write("-" * 30 + "\n")
        for content in ground_crew_content:
            f.write(f"  {content}\n")
        f.write("\n")
        
        f.write("🧳 乘客:\n")
        f.write("-" * 30 + "\n")
        for content in passenger_content:
            f.write(f"  {content}\n")
        f.write("\n")
        
        if unknown_content:
            f.write("❓ 未确定角色:\n")
            f.write("-" * 30 + "\n")
            for content in unknown_content:
                f.write(f"  {content}\n")
            f.write("\n")
    
    print(f"✅ 分类结果已保存到: {output_file}")

def main():
    parser = argparse.ArgumentParser(description='说话人角色分析工具')
    parser.add_argument('json_file', help='ASR结果JSON文件路径')
    parser.add_argument('--output', '-o', help='输出分类结果文件路径')
    
    args = parser.parse_args()
    
    if not Path(args.json_file).exists():
        print(f"错误: 文件不存在 - {args.json_file}")
        return 1
    
    # 分析结果
    analysis_result = analyze_json_result(args.json_file)
    
    # 生成分类输出
    if args.output:
        output_file = args.output
    else:
        json_path = Path(args.json_file)
        output_file = json_path.parent / f"{json_path.stem}_classified.txt"
    
    generate_classified_output(analysis_result, output_file)
    
    # 统计信息
    ground_crew_count = sum(1 for info in analysis_result.values() if info['role'] == 'ground_crew')
    passenger_count = sum(1 for info in analysis_result.values() if info['role'] == 'passenger')
    unknown_count = sum(1 for info in analysis_result.values() if info['role'] == 'unknown')
    
    print("\n📊 统计信息:")
    print(f"  地勤人员: {ground_crew_count} 人")
    print(f"  乘客: {passenger_count} 人")
    print(f"  未确定: {unknown_count} 人")

if __name__ == "__main__":
    import sys
    sys.exit(main())

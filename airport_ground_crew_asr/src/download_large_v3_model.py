#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载Whisper Large-v3模型
确保模型在本地可用，提高加载速度
"""

import os
import sys
import logging
import time
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def download_whisper_large_v3():
    """下载Whisper Large-v3模型 - 支持多种下载策略"""
    try:
        print("=" * 70)
        print("下载Whisper Large-v3模型 (支持断点续传)")
        print("=" * 70)

        # 检查faster-whisper是否可用
        try:
            from faster_whisper import WhisperModel
            logger.info("✓ faster-whisper可用")
        except ImportError:
            logger.error("✗ faster-whisper未安装")
            logger.info("请先安装: pip install faster-whisper")
            return False

        # 检查本地模型是否已存在
        model_path = os.path.expanduser("~/.cache/huggingface/hub/models--Systran--faster-whisper-large-v3")
        if os.path.exists(model_path):
            logger.info(f"✓ Whisper Large-v3模型已存在: {model_path}")

            # 测试模型加载
            try:
                logger.info("测试模型加载...")
                model = WhisperModel(model_path, device="cpu", compute_type="int8")
                logger.info("✓ 模型加载测试成功")
                return True
            except Exception as e:
                logger.warning(f"⚠ 本地模型加载失败: {e}")
                logger.info("将重新下载模型...")

        # 多种下载策略
        download_strategies = [
            {
                "name": "标准下载",
                "config": {
                    "device": "cpu",
                    "compute_type": "int8",
                    "cpu_threads": 4
                }
            },
            {
                "name": "简化配置下载",
                "config": {
                    "device": "cpu",
                    "compute_type": "int8"
                }
            },
            {
                "name": "最小配置下载",
                "config": {
                    "device": "cpu"
                }
            }
        ]

        for i, strategy in enumerate(download_strategies, 1):
            logger.info(f"尝试策略 {i}: {strategy['name']}")
            logger.info("模型大小约3GB，请耐心等待...")

            start_time = time.time()

            try:
                # 设置环境变量以改善下载稳定性
                os.environ['HF_HUB_DISABLE_PROGRESS_BARS'] = '0'  # 显示进度条
                os.environ['HF_HUB_ENABLE_HF_TRANSFER'] = '1'    # 启用快速传输

                # 创建模型实例会自动下载
                model = WhisperModel("large-v3", **strategy['config'])

                download_time = time.time() - start_time
                logger.info(f"✓ Whisper Large-v3模型下载成功 (策略: {strategy['name']})")
                logger.info(f"下载用时: {download_time:.2f} 秒")

                # 成功后跳出循环
                break

            except Exception as e:
                logger.error(f"✗ 策略 {i} 失败: {e}")
                if i < len(download_strategies):
                    logger.info(f"尝试下一个策略...")
                    time.sleep(2)  # 等待2秒后重试
                else:
                    logger.error("所有下载策略都失败了")
                    return False

        # 验证模型
        logger.info("验证模型功能...")

        # 创建测试音频文件路径
        test_audio = "../data/机场地勤音频.WAV"
        if os.path.exists(test_audio):
            logger.info("使用测试音频验证模型...")

            # 进行简短转录测试
            segments, info = model.transcribe(
                test_audio,
                language="zh",
                beam_size=1,  # 使用较小的beam_size加快测试
                best_of=1,
                temperature=0.0
            )

            # 获取前几个片段
            test_segments = []
            for i, segment in enumerate(segments):
                if i >= 3:  # 只测试前3个片段
                    break
                test_segments.append({
                    'start': segment.start,
                    'end': segment.end,
                    'text': segment.text
                })

            if test_segments:
                logger.info("✓ 模型验证成功")
                logger.info(f"检测语言: {info.language}")
                logger.info(f"测试转录片段数: {len(test_segments)}")

                for i, seg in enumerate(test_segments, 1):
                    logger.info(f"  {i}. [{seg['start']:.1f}s-{seg['end']:.1f}s] {seg['text']}")
            else:
                logger.warning("⚠ 模型验证：未获得转录结果")
        else:
            logger.info("未找到测试音频文件，跳过功能验证")

        return True

    except Exception as e:
        logger.error(f"✗ 模型下载失败: {e}")

        # 提供解决建议
        logger.info("\n可能的解决方案:")
        logger.info("1. 检查网络连接")
        logger.info("2. 确保有足够的磁盘空间 (至少4GB)")
        logger.info("3. 如果在中国大陆，可能需要配置代理")
        logger.info("4. 尝试使用VPN或更换网络环境")

        return False

def compare_models():
    """比较不同Whisper模型版本"""
    print("\n" + "=" * 70)
    print("Whisper模型版本对比")
    print("=" * 70)

    print("┌─────────────────┬─────────────────┬─────────────────┬─────────────────┐")
    print("│     特性        │   Large-v2      │   Large-v3      │      改进       │")
    print("├─────────────────┼─────────────────┼─────────────────┼─────────────────┤")
    print("│   发布时间      │    2023年2月    │    2023年11月   │      最新       │")
    print("│   模型大小      │     ~3GB        │     ~3GB        │      相同       │")
    print("│   准确性        │      优秀       │     更优秀      │    提升10-20%   │")
    print("│   时间戳精度    │      良好       │     更精确      │      改进       │")
    print("│   噪音抗性      │      良好       │     更强        │      改进       │")
    print("│   推理速度      │      基准       │     更快        │    优化架构     │")
    print("│   中文支持      │      优秀       │     更优秀      │    特别优化     │")
    print("│   多语言支持    │      99种       │     99种        │      相同       │")
    print("└─────────────────┴─────────────────┴─────────────────┴─────────────────┘")

    print("\nLarge-v3的主要改进:")
    print("✓ 更高的转录准确性，特别是在复杂语境下")
    print("✓ 更精确的时间戳对齐")
    print("✓ 更强的噪音环境适应性")
    print("✓ 更快的推理速度")
    print("✓ 更好的中文和多语言混合识别")
    print("✓ 减少了幻觉现象（生成不存在的内容）")

def main():
    """主函数"""
    print("Whisper Large-v3模型下载工具")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # 显示模型对比
    compare_models()

    # 下载模型
    success = download_whisper_large_v3()

    print("\n" + "=" * 70)
    print("下载总结")
    print("=" * 70)

    if success:
        print("🎉 Whisper Large-v3模型准备完成！")
        print("\n优势:")
        print("✓ 比Large-v2准确性提升10-20%")
        print("✓ 更好的中文识别效果")
        print("✓ 更精确的时间戳")
        print("✓ 更强的噪音抗性")
        print("✓ 更快的推理速度")

        print("\n下一步:")
        print("运行: python integrated_asr_system.py")
        print("或运行: python improved_asr.py")

    else:
        print("❌ Whisper Large-v3模型下载失败")
        print("\n备选方案:")
        print("1. 检查网络连接后重试")
        print("2. 暂时使用Large-v2模型")
        print("3. 联系技术支持")

    print("=" * 70)

if __name__ == "__main__":
    main()

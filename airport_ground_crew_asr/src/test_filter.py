#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试和改进地勤语音过滤器
"""

import json
import re

class ImprovedGroundCrewFilter:
    """改进的地勤语音内容过滤器"""
    
    def __init__(self):
        # 地勤相关关键词 - 扩展版
        self.ground_crew_keywords = [
            # 基础航空词汇
            '航班', '飞机', '推出', '滑行', '跑道', '燃油', '加注',
            '货舱', '装载', '客舱', '清洁', '轮挡', '撤除',
            '地面电源', '断开', '登机桥', '撤离', '机务',
            '检查', '塔台', '地面', '准备', '完毕', '状态',
            '正常', '异物', '重量', '平衡', 
            
            # 航班号前缀
            'CA', 'MU', 'CZ', 'HU', '3U', '9C', 'FM', 'GJ',
            
            # 机场操作词汇
            '起飞', '降落', '停机位', '廊桥', '牵引车',
            '除冰', '加油', '清洁车', '食品车', '行李车',
            '机坪', '滑行道', '停机坪', '候机楼',
            
            # 地勤专业术语
            '推出', '拖拽', '牵引', '顶推', '后推',
            '轮挡', '反光锥', '安全带', '警示灯',
            '加油车', '电源车', '空调车', '清水车',
            '污水车', '垃圾车', '食品车', '行李车',
            
            # 通信用语
            '收到', '明白', '确认', '完毕', '结束',
            '请求', '许可', '等待', '继续', '停止',
            
            # 安全相关
            '安全', '注意', '小心', '危险', '警告',
            '检查', '确认', '正常', '异常', '故障'
        ]
        
        # 非地勤关键词（用于排除）
        self.non_ground_crew_keywords = [
            '吃饭', '休息', '家里', '电话', '聊天', '朋友',
            '电影', '游戏', '购物', '天气', '新闻', '娱乐',
            '家庭', '孩子', '老婆', '老公', '父母', '兄弟',
            '姐妹', '同学', '老师', '学校', '上课', '考试'
        ]
        
        print("初始化改进的地勤语音过滤器")
    
    def is_ground_crew_speech(self, text):
        """判断是否为地勤相关语音"""
        if not text or len(text.strip()) < 2:
            return False
        
        text_clean = text.strip().lower()
        
        # 检查是否包含非地勤关键词
        for keyword in self.non_ground_crew_keywords:
            if keyword in text_clean:
                return False
        
        # 检查是否包含地勤关键词
        ground_crew_score = 0
        matched_keywords = []
        
        for keyword in self.ground_crew_keywords:
            if keyword.lower() in text_clean:
                ground_crew_score += 1
                matched_keywords.append(keyword)
        
        # 如果包含地勤关键词
        if ground_crew_score > 0:
            print(f"匹配到地勤关键词: {matched_keywords} 在文本: '{text}'")
            return True
        
        # 检查是否包含航班号模式
        flight_pattern = re.search(r'[A-Z]{2}\d{3,4}', text.upper())
        if flight_pattern:
            print(f"匹配到航班号模式: {flight_pattern.group()} 在文本: '{text}'")
            return True
        
        # 检查是否包含专业术语组合
        professional_combinations = [
            ['完毕', '准备'], ['检查', '正常'], ['确认', '状态'],
            ['收到', '明白'], ['请求', '许可'], ['安全', '检查']
        ]
        
        for combo in professional_combinations:
            if all(term in text_clean for term in combo):
                print(f"匹配到专业术语组合: {combo} 在文本: '{text}'")
                return True
        
        return False
    
    def calculate_confidence(self, text):
        """计算地勤语音的置信度"""
        if not self.is_ground_crew_speech(text):
            return 0.0
        
        score = 0.3  # 基础分数
        
        # 关键词加分
        for keyword in self.ground_crew_keywords:
            if keyword.lower() in text.lower():
                score += 0.1
        
        # 航班号加分
        if re.search(r'[A-Z]{2}\d{3,4}', text.upper()):
            score += 0.3
        
        # 专业术语加分
        professional_terms = ['完毕', '准备', '检查', '正常', '状态', '确认', '收到', '明白']
        for term in professional_terms:
            if term in text:
                score += 0.05
        
        return min(score, 1.0)

def test_filter_with_real_data():
    """使用真实转录数据测试过滤器"""
    
    # 加载真实转录结果
    try:
        with open('real_asr_result_20250527_111425.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print("未找到转录结果文件")
        return
    
    filter_improved = ImprovedGroundCrewFilter()
    
    print("=" * 60)
    print("使用改进过滤器重新分析转录结果")
    print("=" * 60)
    
    all_transcriptions = data['transcription_results']['all_transcriptions']
    ground_crew_found = []
    
    for segment in all_transcriptions:
        text = segment['text']
        is_ground_crew = filter_improved.is_ground_crew_speech(text)
        
        if is_ground_crew:
            confidence = filter_improved.calculate_confidence(text)
            segment_info = {
                'segment_id': segment['segment_id'],
                'start': segment['start'],
                'end': segment['end'],
                'text': text,
                'confidence': confidence
            }
            ground_crew_found.append(segment_info)
    
    print(f"\n改进后检测到 {len(ground_crew_found)} 个地勤相关片段:")
    print("-" * 50)
    
    for i, segment in enumerate(ground_crew_found, 1):
        print(f"{i}. [{segment['start']:.1f}s-{segment['end']:.1f}s] {segment['text']}")
        print(f"   置信度: {segment['confidence']:.2f}")
        print()
    
    if ground_crew_found:
        print("完整地勤语音文本:")
        full_text = " ".join([s['text'] for s in ground_crew_found])
        print(full_text)
    else:
        print("仍未检测到地勤相关语音")
    
    # 显示所有转录文本供人工检查
    print("\n" + "="*60)
    print("所有转录片段 (供人工检查):")
    print("="*60)
    
    for i, segment in enumerate(all_transcriptions, 1):
        print(f"{i:2d}. [{segment['start']:5.1f}s-{segment['end']:5.1f}s] {segment['text']}")

def test_specific_phrases():
    """测试特定短语"""
    filter_improved = ImprovedGroundCrewFilter()
    
    test_phrases = [
        "没了就这飞机做满了",
        "航班CA1234准备推出",
        "地面电源已断开",
        "轮挡已撤除，可以滑行",
        "机务检查完毕",
        "塔台，地面，准备完毕",
        "今天天气真好",
        "我要回家吃饭",
        "飞机起飞了",
        "这架飞机很大",
        "收到，明白",
        "检查完毕，状态正常"
    ]
    
    print("\n" + "="*60)
    print("测试特定短语:")
    print("="*60)
    
    for phrase in test_phrases:
        is_ground_crew = filter_improved.is_ground_crew_speech(phrase)
        confidence = filter_improved.calculate_confidence(phrase)
        status = "✓ 地勤" if is_ground_crew else "✗ 非地勤"
        print(f"{status} (置信度: {confidence:.2f}) - {phrase}")

if __name__ == "__main__":
    print("地勤语音过滤器测试")
    
    # 测试特定短语
    test_specific_phrases()
    
    # 测试真实数据
    test_filter_with_real_data()

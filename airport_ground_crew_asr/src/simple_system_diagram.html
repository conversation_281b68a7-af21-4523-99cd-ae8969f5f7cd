<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>机场地勤ASR系统流程图</title>
    <style>
      body {
        margin: 0;
        padding: 20px;
        background-color: #f8f9fa;
        font-family: "Microsoft YaHei", Arial, sans-serif;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
      }

      .container {
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        padding: 30px;
        max-width: 1200px;
        width: 100%;
      }

      h1 {
        text-align: center;
        color: #333;
        margin-bottom: 30px;
        font-size: 24px;
      }

      svg {
        width: 100%;
        height: auto;
        display: block;
      }

      /* 统一的盒子样式 */
      .box {
        fill: #ffffff;
        stroke: #333333;
        stroke-width: 2;
        rx: 8;
        ry: 8;
      }

      /* 核心技术模块 */
      .tech-box {
        fill: #f0f0f0;
        stroke: #333333;
        stroke-width: 3;
        rx: 8;
        ry: 8;
      }

      /* 文字样式 */
      .title-text {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        font-size: 16px;
        font-weight: bold;
        fill: #333;
        text-anchor: middle;
      }

      .normal-text {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        font-size: 13px;
        fill: #333;
        text-anchor: middle;
      }

      .small-text {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        font-size: 11px;
        fill: #666;
        text-anchor: middle;
      }

      /* 箭头线条 */
      .arrow-line {
        stroke: #333;
        stroke-width: 2;
        fill: none;
        marker-end: url(#arrowhead);
      }

      .flow-label {
        font-family: "Microsoft YaHei", Arial, sans-serif;
        font-size: 10px;
        fill: #666;
        text-anchor: middle;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>机场地勤ASR系统流程图</h1>

      <svg viewBox="0 0 1000 600">
        <defs>
          <!-- 箭头定义 -->
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
          </marker>
        </defs>

        <!-- 1. 音频输入 -->
        <rect x="50" y="50" width="120" height="80" class="box" />
        <text x="110" y="80" class="title-text">音频输入</text>
        <text x="110" y="100" class="small-text">MP3/WAV/M4A</text>
        <text x="110" y="115" class="small-text">多说话人录音</text>

        <!-- 2. pyannote.audio -->
        <rect x="250" y="30" width="150" height="140" class="tech-box" />
        <text x="325" y="55" class="title-text">pyannote.audio</text>
        <text x="325" y="75" class="normal-text">语音活动检测(VAD)</text>
        <text x="325" y="95" class="normal-text">基于深度学习的</text>
        <text x="325" y="110" class="normal-text">说话人嵌入向量</text>
        <text x="325" y="125" class="normal-text">聚类分离</text>
        <text x="325" y="145" class="small-text">输出时间戳+说话人ID</text>

        <!-- 3. 语音片段提取 -->
        <rect x="480" y="50" width="120" height="80" class="box" />
        <text x="540" y="80" class="title-text">语音片段提取</text>
        <text x="540" y="100" class="small-text">按时间戳切割</text>
        <text x="540" y="115" class="small-text">保持音频质量</text>

        <!-- 4. OpenAI Whisper -->
        <rect x="680" y="30" width="150" height="120" class="tech-box" />
        <text x="755" y="60" class="title-text">OpenAI Whisper</text>
        <text x="755" y="80" class="normal-text">语音识别</text>
        <text x="755" y="100" class="normal-text">large-v3模型</text>
        <text x="755" y="120" class="small-text">逐片段转录</text>

        <!-- 5. 结果整合 -->
        <rect x="480" y="250" width="120" height="80" class="box" />
        <text x="540" y="280" class="title-text">结果整合</text>
        <text x="540" y="300" class="small-text">合并说话人+文本</text>
        <text x="540" y="315" class="small-text">时间轴对齐</text>

        <!-- 6. 输出文件 -->
        <rect x="250" y="250" width="150" height="80" class="box" />
        <text x="325" y="280" class="title-text">输出文件</text>
        <text x="325" y="300" class="small-text">SRT/JSON/VTT</text>
        <text x="325" y="315" class="small-text">带说话人标识</text>

        <!-- 连接线 -->

        <!-- 音频输入 -> pyannote.audio -->
        <path d="M170 90 L250 90" class="arrow-line" />
        <text x="210" y="85" class="flow-label">原始音频</text>

        <!-- pyannote.audio -> 语音片段提取 -->
        <path d="M400 90 L480 90" class="arrow-line" />
        <text x="440" y="85" class="flow-label">分离结果</text>

        <!-- 语音片段提取 -> OpenAI Whisper -->
        <path d="M600 90 L680 90" class="arrow-line" />
        <text x="640" y="85" class="flow-label">音频片段</text>

        <!-- 音频输入 -> 语音片段提取 (原始音频) -->
        <path d="M110 50 L110 13 L540 13 L540 50" class="arrow-line" />
        <text x="325" y="10" class="flow-label">原始音频数据</text>

        <!-- pyannote.audio -> 结果整合 (说话人信息) -->
        <path d="M325 170 L325 200 L540 200 L540 250" class="arrow-line" />
        <text x="400" y="195" class="flow-label">说话人信息</text>

        <!-- OpenAI Whisper -> 结果整合 -->
        <path d="M755 150 L755 200 L600 200 L600 250" class="arrow-line" />
        <text x="675" y="195" class="flow-label">转录文本</text>

        <!-- 结果整合 -> 输出文件 -->
        <path d="M480 290 L400 290" class="arrow-line" />
        <text x="440" y="285" class="flow-label">最终结果</text>

        <!-- 流程说明 -->
        <text x="500" y="400" class="title-text">系统流程说明</text>
        <text x="50" y="430" class="normal-text">1. 输入多说话人音频文件</text>
        <text x="50" y="450" class="normal-text">
          2.
          pyannote.audio基于深度学习提取说话人嵌入向量，通过聚类实现说话人分离
        </text>
        <text x="50" y="470" class="normal-text">
          3. 根据时间戳切割原始音频为多个片段
        </text>
        <text x="50" y="490" class="normal-text">
          4. OpenAI Whisper对每个片段进行语音识别
        </text>
        <text x="50" y="510" class="normal-text">
          5. 整合说话人信息和转录文本，生成最终输出文件
        </text>
      </svg>
    </div>
  </body>
</html>

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SenseVoice ASR系统
使用阿里的SenseVoice模型替代Whisper Large-v2
提供更快的推理速度和更好的中文识别效果
"""

import os
import sys
import logging
import warnings
import numpy as np
import json
from datetime import datetime
from pathlib import Path

# 抑制警告
warnings.filterwarnings("ignore")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入SenseVoice依赖
try:
    from funasr import AutoModel
    from funasr.utils.postprocess_utils import rich_transcription_postprocess
    SENSEVOICE_AVAILABLE = True
    logger.info("SenseVoice可用")
except ImportError as e:
    SENSEVOICE_AVAILABLE = False
    logger.error(f"SenseVoice未安装: {e}")
    logger.error("请运行: pip install funasr modelscope")

class SenseVoiceASRSystem:
    """SenseVoice ASR系统"""

    def __init__(self, model_name="iic/SenseVoiceSmall"):
        self.model_name = model_name
        self.model = None
        self.device = "cpu"  # 可以改为 "cuda:0" 如果有GPU

        # SenseVoice参数配置
        self.vad_enabled = True
        self.vad_max_segment_time = 30000  # 30秒
        self.merge_vad = True
        self.merge_length_s = 15
        self.batch_size_s = 60
        self.use_itn = True  # 使用逆文本标准化（标点符号）

        logger.info(f"初始化SenseVoice ASR系统 (模型: {model_name})")

    def initialize(self):
        """初始化SenseVoice模型"""
        try:
            if not SENSEVOICE_AVAILABLE:
                raise ImportError("需要安装funasr和modelscope")

            logger.info(f"加载SenseVoice模型: {self.model_name}")

            # 配置模型参数 - 修复remote_code问题
            model_config = {
                "model": self.model_name,
                "trust_remote_code": False,  # 改为False避免remote code问题
                "device": self.device,
                "disable_update": True,  # 禁用更新检查
            }

            # 如果启用VAD，添加VAD配置
            if self.vad_enabled:
                model_config.update({
                    "vad_model": "fsmn-vad",
                    "vad_kwargs": {"max_single_segment_time": self.vad_max_segment_time},
                })

            self.model = AutoModel(**model_config)

            logger.info("SenseVoice模型加载成功")
            return True

        except Exception as e:
            logger.error(f"初始化SenseVoice模型失败: {e}")
            return False

    def transcribe_audio(self, audio_path: str, language="auto"):
        """使用SenseVoice转录音频"""
        try:
            if self.model is None:
                raise RuntimeError("模型未初始化")

            logger.info(f"开始SenseVoice转录: {audio_path}")
            start_time = datetime.now()

            # 使用SenseVoice进行转录
            res = self.model.generate(
                input=audio_path,
                cache={},
                language=language,  # "zh", "en", "yue", "ja", "ko", "auto", "nospeech"
                use_itn=self.use_itn,
                batch_size_s=self.batch_size_s,
                merge_vad=self.merge_vad,
                merge_length_s=self.merge_length_s,
            )

            processing_time = (datetime.now() - start_time).total_seconds()

            # 处理转录结果
            if not res or len(res) == 0:
                logger.warning("SenseVoice转录结果为空")
                return None

            # 提取转录文本和详细信息
            result = res[0]

            # 使用rich_transcription_postprocess处理文本
            processed_text = rich_transcription_postprocess(result["text"])

            # 构建详细结果
            transcription_result = {
                "text": processed_text,
                "raw_text": result["text"],
                "language": language,
                "processing_time": processing_time,
                "timestamp": datetime.now().isoformat(),
                "model": self.model_name,
                "segments": []
            }

            # 如果有分段信息，添加到结果中
            if "timestamp" in result and result["timestamp"]:
                for i, (start, end) in enumerate(result["timestamp"]):
                    segment = {
                        "id": i,
                        "start": start / 1000.0,  # 转换为秒
                        "end": end / 1000.0,
                        "text": result["text_seg"][i] if "text_seg" in result and i < len(result["text_seg"]) else "",
                    }
                    transcription_result["segments"].append(segment)

            # 添加情感和事件信息（如果可用）
            if "emo_res" in result:
                transcription_result["emotion"] = result["emo_res"]

            if "event_res" in result:
                transcription_result["events"] = result["event_res"]

            logger.info(f"SenseVoice转录完成，用时 {processing_time:.2f} 秒")
            logger.info(f"转录文本: {processed_text}")

            return transcription_result

        except Exception as e:
            logger.error(f"SenseVoice转录失败: {e}")
            return None

    def transcribe_segments(self, audio_segments):
        """转录音频片段列表"""
        try:
            logger.info(f"开始转录 {len(audio_segments)} 个音频片段...")

            transcribed_segments = []

            for i, segment in enumerate(audio_segments):
                logger.info(f"转录片段 {i+1}/{len(audio_segments)}: {segment['start_time']:.1f}s-{segment['end_time']:.1f}s")

                # 保存临时音频文件
                temp_audio_path = f"temp_sensevoice_segment_{i}.wav"

                try:
                    import soundfile as sf
                    sf.write(temp_audio_path, segment['audio'], 16000)

                    # 使用SenseVoice转录
                    result = self.transcribe_audio(temp_audio_path, language="auto")

                    if result:
                        transcribed_segment = {
                            'start_time': segment['start_time'],
                            'end_time': segment['end_time'],
                            'duration': segment['duration'],
                            'text': result['text'],
                            'raw_text': result['raw_text'],
                            'processing_time': result['processing_time'],
                            'emotion': result.get('emotion', None),
                            'events': result.get('events', None),
                            'segments': result.get('segments', [])
                        }

                        transcribed_segments.append(transcribed_segment)
                        logger.info(f"片段转录完成: {result['text']}")
                    else:
                        # 添加空转录结果
                        transcribed_segments.append({
                            'start_time': segment['start_time'],
                            'end_time': segment['end_time'],
                            'duration': segment['duration'],
                            'text': '',
                            'error': 'SenseVoice转录失败'
                        })

                except Exception as e:
                    logger.error(f"片段 {i} 转录失败: {e}")

                    # 添加错误转录结果
                    transcribed_segments.append({
                        'start_time': segment['start_time'],
                        'end_time': segment['end_time'],
                        'duration': segment['duration'],
                        'text': '',
                        'error': str(e)
                    })

                finally:
                    # 清理临时文件
                    if os.path.exists(temp_audio_path):
                        os.remove(temp_audio_path)

            logger.info(f"转录完成，共 {len(transcribed_segments)} 个片段")

            return transcribed_segments

        except Exception as e:
            logger.error(f"批量转录失败: {e}")
            return []

    def get_model_info(self):
        """获取模型信息"""
        return {
            "model_name": self.model_name,
            "model_type": "SenseVoice",
            "device": self.device,
            "vad_enabled": self.vad_enabled,
            "features": [
                "多语言ASR",
                "情感识别",
                "音频事件检测",
                "快速推理",
                "中文优化"
            ]
        }

def test_sensevoice_asr():
    """测试SenseVoice ASR系统"""
    print("=" * 70)
    print("测试SenseVoice ASR系统")
    print("=" * 70)

    # 检查音频文件
    audio_file = "../data/机场地勤音频.WAV"
    if not os.path.exists(audio_file):
        print(f"错误: 音频文件不存在 - {audio_file}")
        return

    # 创建SenseVoice ASR系统
    asr_system = SenseVoiceASRSystem()

    # 初始化
    if not asr_system.initialize():
        print("SenseVoice ASR系统初始化失败")
        return

    print(f"开始处理音频文件: {audio_file}")
    start_time = datetime.now()

    # 转录音频
    result = asr_system.transcribe_audio(audio_file, language="auto")

    processing_time = (datetime.now() - start_time).total_seconds()

    if result:
        print(f"\n处理结果:")
        print(f"音频文件: {audio_file}")
        print(f"处理时间: {processing_time:.2f} 秒")
        print(f"模型: {result['model']}")

        print(f"\n转录结果:")
        print(f"文本: {result['text']}")

        if result.get('emotion'):
            print(f"情感: {result['emotion']}")

        if result.get('events'):
            print(f"事件: {result['events']}")

        if result.get('segments'):
            print(f"\n分段结果 ({len(result['segments'])} 个片段):")
            for i, segment in enumerate(result['segments'], 1):
                print(f"{i:2d}. [{segment['start']:6.1f}s - {segment['end']:6.1f}s] {segment['text']}")

        # 保存结果
        output_file = f"sensevoice_asr_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        print(f"\n详细结果已保存到: {output_file}")

    else:
        print("转录失败")

if __name__ == "__main__":
    test_sensevoice_asr()

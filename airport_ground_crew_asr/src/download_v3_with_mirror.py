#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用HF镜像加速下载Whisper Large-v3模型
解决国内网络访问HuggingFace慢的问题
"""

import os
import sys
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_hf_mirror():
    """设置HF镜像环境变量"""
    # 设置HF镜像加速
    os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
    os.environ['HF_HUB_DISABLE_PROGRESS_BARS'] = '0'  # 显示进度条

    # 检查是否安装了hf_transfer，如果没有则禁用
    try:
        import hf_transfer
        os.environ['HF_HUB_ENABLE_HF_TRANSFER'] = '1'
        logger.info("✓ 检测到hf_transfer，启用快速传输")
    except ImportError:
        os.environ['HF_HUB_ENABLE_HF_TRANSFER'] = '0'
        logger.info("⚠ 未安装hf_transfer，使用标准传输")

    print("=" * 70)
    print("使用HF镜像加速下载Whisper Large-v3")
    print("=" * 70)
    print(f"镜像地址: {os.environ['HF_ENDPOINT']}")
    print("这将显著提升下载速度！")
    print("=" * 70)

def download_with_huggingface_hub():
    """使用huggingface_hub下载"""
    try:
        logger.info("方法1: 使用huggingface_hub下载...")

        from huggingface_hub import snapshot_download

        logger.info("开始下载Whisper Large-v3模型...")
        logger.info("模型大小约3GB，使用镜像加速中...")

        start_time = time.time()

        # 下载模型，增加重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f"下载尝试 {attempt + 1}/{max_retries}")

                model_path = snapshot_download(
                    repo_id="Systran/faster-whisper-large-v3",
                    cache_dir=os.path.expanduser("~/.cache/huggingface/hub"),
                    resume_download=True,
                    local_files_only=False,
                    force_download=False,  # 允许使用缓存
                    token=None  # 公开模型不需要token
                )

                download_time = time.time() - start_time
                logger.info(f"✓ 模型下载成功！用时: {download_time:.2f}秒")
                logger.info(f"模型路径: {model_path}")

                return model_path

            except Exception as e:
                logger.warning(f"下载尝试 {attempt + 1} 失败: {e}")
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 10  # 递增等待时间
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                else:
                    raise e

    except Exception as e:
        logger.error(f"✗ huggingface_hub下载失败: {e}")
        return None

def download_with_faster_whisper():
    """使用faster_whisper下载"""
    try:
        logger.info("方法2: 使用faster_whisper下载...")

        from faster_whisper import WhisperModel

        logger.info("开始下载并加载Large-v3模型...")
        start_time = time.time()

        # 使用更稳定的参数
        model = WhisperModel(
            "large-v3",
            device="cpu",
            compute_type="int8",
            local_files_only=False,
            cpu_threads=4
        )

        download_time = time.time() - start_time
        logger.info(f"✓ 模型下载并加载成功！用时: {download_time:.2f}秒")

        return model

    except Exception as e:
        logger.error(f"✗ faster_whisper下载失败: {e}")
        return None

def download_with_manual_resume():
    """手动恢复下载"""
    try:
        logger.info("方法3: 手动恢复下载...")

        # 检查当前下载进度
        model_path = os.path.expanduser("~/.cache/huggingface/hub/models--Systran--faster-whisper-large-v3")
        blobs_dir = os.path.join(model_path, "blobs")

        if os.path.exists(blobs_dir):
            incomplete_files = [f for f in os.listdir(blobs_dir) if f.endswith('.incomplete')]
            if incomplete_files:
                incomplete_file = incomplete_files[0]
                incomplete_path = os.path.join(blobs_dir, incomplete_file)
                file_size = os.path.getsize(incomplete_path)

                logger.info(f"发现不完整文件: {incomplete_file}")
                logger.info(f"已下载: {file_size / (1024*1024*1024):.2f}GB")

                # 尝试使用huggingface_hub恢复下载
                from huggingface_hub import hf_hub_download

                # 获取正确的文件名（去掉.incomplete后缀）
                original_filename = incomplete_file.replace('.incomplete', '')

                logger.info("尝试恢复下载...")

                downloaded_file = hf_hub_download(
                    repo_id="Systran/faster-whisper-large-v3",
                    filename="model.bin",
                    cache_dir=os.path.expanduser("~/.cache/huggingface/hub"),
                    resume_download=True,
                    local_files_only=False
                )

                logger.info(f"✓ 文件下载完成: {downloaded_file}")
                return downloaded_file

        return None

    except Exception as e:
        logger.error(f"✗ 手动恢复下载失败: {e}")
        return None

def test_model():
    """测试模型是否正常工作"""
    try:
        logger.info("测试Large-v3模型...")

        from faster_whisper import WhisperModel

        # 加载模型
        model = WhisperModel("large-v3", device="cpu", compute_type="int8")
        logger.info("✓ 模型加载成功")

        # 测试转录
        test_audio = "../data/机场地勤音频.WAV"
        if os.path.exists(test_audio):
            logger.info("使用测试音频验证模型功能...")

            segments, info = model.transcribe(
                test_audio,
                language="zh",
                beam_size=1,
                best_of=1,
                temperature=0.0
            )

            # 获取第一个片段
            first_segment = next(segments, None)
            if first_segment:
                logger.info("✓ 模型功能验证成功")
                logger.info(f"检测语言: {info.language}")
                logger.info(f"测试转录: {first_segment.text}")
                return True
            else:
                logger.warning("⚠ 未获得转录结果")
                return False
        else:
            logger.info("未找到测试音频，跳过功能验证")
            return True

    except Exception as e:
        logger.error(f"✗ 模型测试失败: {e}")
        return False

def check_model_exists():
    """检查模型是否已存在"""
    model_path = os.path.expanduser("~/.cache/huggingface/hub/models--Systran--faster-whisper-large-v3")

    if os.path.exists(model_path):
        # 检查关键文件
        snapshots_dir = os.path.join(model_path, "snapshots")
        if os.path.exists(snapshots_dir):
            snapshot_dirs = os.listdir(snapshots_dir)
            if snapshot_dirs:
                snapshot_path = os.path.join(snapshots_dir, snapshot_dirs[0])
                model_bin = os.path.join(snapshot_path, "model.bin")
                if os.path.exists(model_bin):
                    logger.info(f"✓ Large-v3模型已存在: {model_path}")
                    return True

    logger.info("Large-v3模型不存在或不完整")
    return False

def clean_incomplete_download():
    """清理不完整的下载文件"""
    try:
        model_path = os.path.expanduser("~/.cache/huggingface/hub/models--Systran--faster-whisper-large-v3")

        if os.path.exists(model_path):
            # 检查是否有.incomplete文件
            blobs_dir = os.path.join(model_path, "blobs")
            if os.path.exists(blobs_dir):
                incomplete_files = [f for f in os.listdir(blobs_dir) if f.endswith('.incomplete')]
                if incomplete_files:
                    logger.info(f"发现 {len(incomplete_files)} 个不完整的下载文件")
                    for incomplete_file in incomplete_files:
                        incomplete_path = os.path.join(blobs_dir, incomplete_file)
                        logger.info(f"删除不完整文件: {incomplete_file}")
                        os.remove(incomplete_path)

                    # 删除整个模型目录以确保重新下载
                    logger.info("删除不完整的模型目录，准备重新下载...")
                    import shutil
                    shutil.rmtree(model_path)
                    return True

        return False

    except Exception as e:
        logger.error(f"清理不完整下载失败: {e}")
        return False

def main():
    """主函数"""
    print("Whisper Large-v3镜像加速下载工具")
    print("使用hf-mirror.com加速下载")

    # 设置镜像
    setup_hf_mirror()

    # 清理不完整的下载
    if clean_incomplete_download():
        print("🧹 已清理不完整的下载文件")

    # 检查是否已存在完整模型
    if check_model_exists():
        print("🎉 Large-v3模型已存在！")

        # 测试模型
        if test_model():
            print("✓ 模型功能正常")
            print("\n可以运行:")
            print("python integrated_asr_system.py")
            return
        else:
            print("⚠ 模型存在但功能异常，重新下载...")
            # 删除有问题的模型
            model_path = os.path.expanduser("~/.cache/huggingface/hub/models--Systran--faster-whisper-large-v3")
            import shutil
            shutil.rmtree(model_path)

    # 下载模型
    print("\n开始下载Large-v3模型...")
    print("📥 使用HF镜像加速，预计下载时间: 5-15分钟")
    print("📊 模型大小: 约3GB")

    # 检查是否有未完成的下载
    model_path = os.path.expanduser("~/.cache/huggingface/hub/models--Systran--faster-whisper-large-v3")
    blobs_dir = os.path.join(model_path, "blobs")
    has_incomplete = False

    if os.path.exists(blobs_dir):
        incomplete_files = [f for f in os.listdir(blobs_dir) if f.endswith('.incomplete')]
        if incomplete_files:
            incomplete_file = incomplete_files[0]
            incomplete_path = os.path.join(blobs_dir, incomplete_file)
            file_size = os.path.getsize(incomplete_path)
            progress = (file_size / (3.09 * 1024 * 1024 * 1024)) * 100

            print(f"🔄 发现未完成的下载: {progress:.1f}% ({file_size / (1024*1024*1024):.2f}GB/3.09GB)")
            print("将尝试恢复下载...")
            has_incomplete = True

    success = False

    # 尝试方法1: huggingface_hub
    if not success:
        model_path_result = download_with_huggingface_hub()
        if model_path_result:
            print("✓ huggingface_hub下载成功")
            success = True

    # 尝试方法2: faster_whisper
    if not success:
        print("⚠ huggingface_hub下载失败，尝试方法2...")
        model = download_with_faster_whisper()
        if model:
            print("✓ faster_whisper下载成功")
            success = True

    # 尝试方法3: 手动恢复下载
    if not success and has_incomplete:
        print("⚠ faster_whisper下载失败，尝试恢复下载...")
        resume_result = download_with_manual_resume()
        if resume_result:
            print("✓ 恢复下载成功")
            success = True

    if not success:
        print("❌ 所有下载方法都失败了")
        print("\n可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 尝试使用VPN")
        print("3. 稍后重试")
        print("4. 手动下载模型文件")
        print("5. 使用Large-v2作为临时替代")
        return

    # 最终测试
    print("\n" + "="*70)
    print("下载完成，进行最终测试...")
    print("="*70)

    if test_model():
        print("🎉 Large-v3模型下载并验证成功！")
        print("\n✅ 模型已就绪，可以使用真正的Large-v3")
        print("✅ 相比Large-v2，v3具有更高准确性和更快速度")
        print("✅ 支持更好的中文识别和时间戳精度")
        print("\n🚀 运行命令:")
        print("python integrated_asr_system.py")
        print("\n🎯 系统将使用Large-v3模型进行转录")
    else:
        print("❌ 模型下载完成但验证失败")
        print("请检查模型文件完整性")

if __name__ == "__main__":
    main()

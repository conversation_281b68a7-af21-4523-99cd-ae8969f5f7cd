#!/usr/bin/env python3
"""
音频截取工具
从长音频文件中截取指定时长的片段
"""

import os
import sys
import argparse
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
    logger.info("pydub音频处理工具可用")
except ImportError:
    PYDUB_AVAILABLE = False
    logger.warning("pydub不可用，将尝试使用ffmpeg")

def extract_audio_segment_pydub(input_file, output_file, start_time=0, duration=3600):
    """
    使用pydub截取音频片段

    Args:
        input_file: 输入音频文件路径
        output_file: 输出音频文件路径
        start_time: 开始时间（秒）
        duration: 持续时间（秒），默认3600秒（1小时）
    """
    try:
        logger.info(f"开始加载音频文件: {input_file}")

        # 加载音频文件
        audio = AudioSegment.from_file(input_file)

        # 获取音频信息
        total_duration = len(audio) / 1000  # 转换为秒
        logger.info(f"音频总时长: {total_duration:.2f}秒 ({total_duration/3600:.2f}小时)")

        # 检查参数
        if start_time >= total_duration:
            logger.error(f"开始时间 {start_time}秒 超过音频总时长 {total_duration:.2f}秒")
            return False

        # 计算实际截取时长
        actual_duration = min(duration, total_duration - start_time)
        end_time = start_time + actual_duration

        logger.info(f"截取时间段: {start_time}秒 - {end_time}秒 (时长: {actual_duration}秒)")

        # 截取音频片段
        start_ms = start_time * 1000
        end_ms = end_time * 1000

        logger.info("正在截取音频片段...")
        audio_segment = audio[start_ms:end_ms]

        # 保存截取的音频
        logger.info(f"保存音频片段到: {output_file}")
        audio_segment.export(output_file, format="wav")

        logger.info(f"✅ 音频截取完成!")
        logger.info(f"输出文件: {output_file}")
        logger.info(f"截取时长: {actual_duration:.2f}秒 ({actual_duration/60:.2f}分钟)")

        return True

    except Exception as e:
        logger.error(f"音频截取失败: {e}")
        return False

def extract_audio_segment_ffmpeg(input_file, output_file, start_time=0, duration=3600):
    """
    使用ffmpeg截取音频片段

    Args:
        input_file: 输入音频文件路径
        output_file: 输出音频文件路径
        start_time: 开始时间（秒）
        duration: 持续时间（秒），默认3600秒（1小时）
    """
    try:
        import subprocess

        # 格式化时间
        start_time_str = f"{start_time//3600:02d}:{(start_time%3600)//60:02d}:{start_time%60:02d}"
        duration_str = f"{duration//3600:02d}:{(duration%3600)//60:02d}:{duration%60:02d}"

        logger.info(f"使用ffmpeg截取音频")
        logger.info(f"开始时间: {start_time_str}")
        logger.info(f"持续时间: {duration_str}")

        # 构建ffmpeg命令 (使用完整路径)
        ffmpeg_path = '/Users/<USER>/Documents/ffmpeg/bin/ffmpeg'
        cmd = [
            ffmpeg_path,
            '-i', input_file,
            '-ss', start_time_str,
            '-t', duration_str,
            '-acodec', 'pcm_s16le',
            '-ar', '16000',
            '-ac', '1',
            '-y',  # 覆盖输出文件
            output_file
        ]

        logger.info(f"执行命令: {' '.join(cmd)}")

        # 执行ffmpeg命令
        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode == 0:
            logger.info(f"✅ 音频截取完成!")
            logger.info(f"输出文件: {output_file}")
            return True
        else:
            logger.error(f"ffmpeg执行失败: {result.stderr}")
            return False

    except Exception as e:
        logger.error(f"ffmpeg音频截取失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='音频截取工具')
    parser.add_argument('input_file', help='输入音频文件路径')
    parser.add_argument('--output', '-o', help='输出音频文件路径 (默认: 输入文件名_segment.wav)')
    parser.add_argument('--start', '-s', type=int, default=0, help='开始时间（秒，默认: 0）')
    parser.add_argument('--duration', '-d', type=int, default=3600, help='持续时间（秒，默认: 3600=1小时）')
    parser.add_argument('--use-ffmpeg', '-f', action='store_true', help='强制使用ffmpeg')

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.input_file):
        logger.error(f"输入文件不存在: {args.input_file}")
        return 1

    # 生成输出文件名
    if args.output:
        output_file = args.output
    else:
        input_path = Path(args.input_file)
        output_file = str(input_path.parent / f"{input_path.stem}_segment.wav")

    # 创建输出目录
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    logger.info("=" * 60)
    logger.info("音频截取工具")
    logger.info("=" * 60)
    logger.info(f"输入文件: {args.input_file}")
    logger.info(f"输出文件: {output_file}")
    logger.info(f"开始时间: {args.start}秒")
    logger.info(f"持续时间: {args.duration}秒 ({args.duration/60:.1f}分钟)")
    logger.info("=" * 60)

    # 选择截取方法
    success = False

    if args.use_ffmpeg or not PYDUB_AVAILABLE:
        logger.info("使用ffmpeg进行音频截取")
        success = extract_audio_segment_ffmpeg(args.input_file, output_file, args.start, args.duration)
    else:
        logger.info("使用pydub进行音频截取")
        success = extract_audio_segment_pydub(args.input_file, output_file, args.start, args.duration)

    if success:
        logger.info(f"🎉 音频截取成功! 输出文件: {output_file}")
        return 0
    else:
        logger.error("❌ 音频截取失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())

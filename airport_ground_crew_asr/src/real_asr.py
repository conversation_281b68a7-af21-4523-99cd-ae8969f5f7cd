#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实的机场地勤ASR系统
使用faster-whisper进行实际的语音识别

功能特点：
1. 使用faster-whisper进行真实ASR
2. 音频预处理和分段
3. 模拟说话人分离
4. 地勤语音内容识别和过滤
"""

import os
import sys
import logging
import warnings
import numpy as np
import json
from datetime import datetime
from pathlib import Path
import re

# 抑制警告
warnings.filterwarnings("ignore")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 尝试导入音频处理库
try:
    import librosa
    import soundfile as sf
    AUDIO_LIBS_AVAILABLE = True
    logger.info("音频处理库加载成功")
except ImportError:
    AUDIO_LIBS_AVAILABLE = False
    logger.warning("音频处理库未安装")

# 尝试导入faster-whisper
try:
    from faster_whisper import WhisperModel
    WHISPER_AVAILABLE = True
    logger.info("Faster-Whisper加载成功")
except ImportError:
    WHISPER_AVAILABLE = False
    logger.warning("Faster-Whisper未安装")

class RealAudioProcessor:
    """真实音频处理器"""

    def __init__(self):
        self.sample_rate = 16000
        self.chunk_duration = 30  # 30秒分段
        logger.info("初始化真实音频处理器")

    def load_and_preprocess(self, audio_path: str):
        """加载和预处理音频"""
        try:
            if not AUDIO_LIBS_AVAILABLE:
                raise ImportError("需要安装librosa和soundfile")

            logger.info(f"加载音频文件: {audio_path}")

            # 加载音频
            audio, sr = librosa.load(audio_path, sr=self.sample_rate)
            duration = len(audio) / sr

            logger.info(f"音频时长: {duration:.2f}秒, 采样率: {sr}Hz")

            # 基本预处理
            audio = self._normalize_audio(audio)
            audio = self._remove_silence(audio, sr)

            return audio, sr, duration

        except Exception as e:
            logger.error(f"音频预处理失败: {e}")
            raise

    def _normalize_audio(self, audio):
        """音频标准化"""
        # 移除直流分量
        audio = audio - np.mean(audio)

        # 标准化到[-1, 1]
        max_val = np.max(np.abs(audio))
        if max_val > 0:
            audio = audio / max_val * 0.95

        return audio

    def _remove_silence(self, audio, sr):
        """移除静音段"""
        try:
            # 使用librosa的trim功能移除开头和结尾的静音
            audio_trimmed, _ = librosa.effects.trim(audio, top_db=20)
            return audio_trimmed
        except:
            return audio

    def split_audio(self, audio, sr):
        """将音频分割成小段"""
        chunk_samples = int(self.chunk_duration * sr)
        chunks = []

        for i in range(0, len(audio), chunk_samples):
            chunk = audio[i:i + chunk_samples]
            if len(chunk) > sr:  # 至少1秒
                start_time = i / sr
                end_time = min((i + len(chunk)) / sr, len(audio) / sr)
                chunks.append({
                    'audio': chunk,
                    'start': start_time,
                    'end': end_time,
                    'duration': end_time - start_time
                })

        logger.info(f"音频分割为 {len(chunks)} 个片段")
        return chunks

class RealWhisperASR:
    """真实的Whisper ASR引擎"""

    def __init__(self, model_size="base"):
        self.model_size = model_size
        self.model = None
        self.device = "cpu"  # Mac上使用CPU
        self.compute_type = "int8"  # 节省内存
        logger.info(f"初始化Whisper ASR引擎 (模型: {model_size})")

    def initialize(self):
        """初始化Whisper模型"""
        try:
            if not WHISPER_AVAILABLE:
                raise ImportError("需要安装faster-whisper")

            logger.info(f"正在加载Whisper模型: {self.model_size}")

            # 尝试加载模型，如果失败则使用模拟模式
            try:
                self.model = WhisperModel(
                    self.model_size,
                    device=self.device,
                    compute_type=self.compute_type,
                    local_files_only=False  # 允许下载
                )
                logger.info("Whisper模型加载成功")
                return True
            except Exception as model_error:
                logger.warning(f"无法加载Whisper模型: {model_error}")
                logger.info("切换到模拟ASR模式")
                self.model = "mock"  # 标记为模拟模式
                return True

        except Exception as e:
            logger.error(f"初始化Whisper模型失败: {e}")
            return False

    def transcribe_chunk(self, audio_chunk, sample_rate):
        """转录音频片段"""
        try:
            if self.model is None:
                raise RuntimeError("模型未初始化")

            # faster-whisper需要音频数据为float32格式
            audio_float32 = audio_chunk.astype(np.float32)

            # 执行转录
            segments, info = self.model.transcribe(
                audio_float32,
                language="zh",  # 中文
                beam_size=5,
                word_timestamps=True,
                vad_filter=True,  # 语音活动检测
                vad_parameters=dict(min_silence_duration_ms=500)
            )

            # 收集结果
            transcription_text = ""
            word_segments = []

            for segment in segments:
                transcription_text += segment.text

                # 收集词级时间戳
                if hasattr(segment, 'words') and segment.words:
                    for word in segment.words:
                        word_segments.append({
                            'word': word.word,
                            'start': word.start,
                            'end': word.end,
                            'probability': word.probability
                        })

            result = {
                'text': transcription_text.strip(),
                'language': info.language,
                'language_probability': info.language_probability,
                'duration': info.duration,
                'words': word_segments
            }

            return result

        except Exception as e:
            logger.error(f"转录失败: {e}")
            return {
                'text': '',
                'error': str(e)
            }

    def transcribe_file(self, audio_path: str):
        """转录整个音频文件"""
        try:
            if self.model is None or self.model == "mock":
                raise RuntimeError("模型未初始化")

            logger.info(f"开始转录音频文件: {audio_path}")

            # 使用faster-whisper转录整个文件
            segments, info = self.model.transcribe(
                audio_path,
                language="zh",  # 中文
                beam_size=5,
                word_timestamps=True,
                vad_filter=True,  # 语音活动检测
                vad_parameters=dict(min_silence_duration_ms=500)
            )

            # 收集所有片段
            all_segments = []
            full_text = ""

            for segment in segments:
                segment_info = {
                    'start': segment.start,
                    'end': segment.end,
                    'text': segment.text,
                    'avg_logprob': segment.avg_logprob,
                    'no_speech_prob': segment.no_speech_prob
                }

                # 添加词级时间戳
                if hasattr(segment, 'words') and segment.words:
                    segment_info['words'] = [
                        {
                            'word': word.word,
                            'start': word.start,
                            'end': word.end,
                            'probability': word.probability
                        }
                        for word in segment.words
                    ]

                all_segments.append(segment_info)
                full_text += segment.text

            result = {
                'text': full_text.strip(),
                'language': info.language,
                'language_probability': info.language_probability,
                'duration': info.duration,
                'segments': all_segments,
                'vad_options': info.vad_options if hasattr(info, 'vad_options') else None
            }

            logger.info(f"转录完成，检测到 {len(all_segments)} 个语音片段")
            logger.info(f"音频时长: {info.duration:.2f}秒")
            logger.info(f"检测语言: {info.language} (置信度: {info.language_probability:.2f})")

            return result

        except Exception as e:
            logger.error(f"文件转录失败: {e}")
            return {
                'text': '',
                'error': str(e),
                'duration': 0,
                'segments': []
            }

class GroundCrewFilter:
    """地勤语音内容过滤器"""

    def __init__(self):
        # 地勤相关关键词
        self.ground_crew_keywords = [
            '航班', '推出', '滑行', '跑道', '燃油', '加注',
            '货舱', '装载', '客舱', '清洁', '轮挡', '撤除',
            '地面电源', '断开', '登机桥', '撤离', '机务',
            '检查', '塔台', '地面', '准备', '完毕', '状态',
            '正常', '异物', '重量', '平衡', 'CA', 'MU', 'CZ',
            '起飞', '降落', '停机位', '廊桥', '牵引车',
            '除冰', '加油', '清洁车', '食品车', '行李车'
        ]

        # 非地勤关键词（用于排除）
        self.non_ground_crew_keywords = [
            '吃饭', '休息', '家里', '电话', '聊天', '朋友',
            '电影', '游戏', '购物', '天气', '新闻'
        ]

        logger.info("初始化地勤语音过滤器")

    def is_ground_crew_speech(self, text):
        """判断是否为地勤相关语音"""
        if not text or len(text.strip()) < 2:
            return False

        text_lower = text.lower()

        # 检查是否包含非地勤关键词
        for keyword in self.non_ground_crew_keywords:
            if keyword in text_lower:
                return False

        # 检查是否包含地勤关键词
        ground_crew_score = 0
        for keyword in self.ground_crew_keywords:
            if keyword in text_lower:
                ground_crew_score += 1

        # 如果包含地勤关键词，或者文本较短且包含数字/字母（可能是航班号等）
        if ground_crew_score > 0:
            return True

        # 检查是否包含航班号模式
        flight_pattern = re.search(r'[A-Z]{2}\d{3,4}', text.upper())
        if flight_pattern:
            return True

        # 检查是否包含专业术语
        if any(term in text for term in ['完毕', '准备', '检查', '正常', '状态']):
            return True

        return False

    def calculate_confidence(self, text):
        """计算地勤语音的置信度"""
        if not self.is_ground_crew_speech(text):
            return 0.0

        score = 0.5  # 基础分数

        # 关键词加分
        for keyword in self.ground_crew_keywords:
            if keyword in text:
                score += 0.1

        # 航班号加分
        if re.search(r'[A-Z]{2}\d{3,4}', text.upper()):
            score += 0.2

        # 专业术语加分
        professional_terms = ['完毕', '准备', '检查', '正常', '状态', '确认']
        for term in professional_terms:
            if term in text:
                score += 0.05

        return min(score, 1.0)

class RealASRSystem:
    """真实的机场地勤ASR系统"""

    def __init__(self, whisper_model="base"):
        self.audio_processor = RealAudioProcessor()
        self.asr_engine = RealWhisperASR(whisper_model)
        self.ground_crew_filter = GroundCrewFilter()
        logger.info("真实ASR系统初始化完成")

    def initialize(self):
        """初始化系统"""
        return self.asr_engine.initialize()

    def process_audio(self, audio_path: str):
        """处理音频文件"""
        try:
            logger.info(f"开始处理音频文件: {audio_path}")
            start_time = datetime.now()

            # 获取文件基本信息
            file_size = os.path.getsize(audio_path) / (1024*1024)
            logger.info(f"音频文件大小: {file_size:.2f} MB")

            # 直接使用Whisper处理整个文件，避免librosa的兼容性问题
            logger.info("直接使用Whisper处理音频文件")

            # 使用Whisper的内置音频处理
            if self.asr_engine.model != "mock":
                # 真实Whisper转录
                transcription = self.asr_engine.transcribe_file(audio_path)
                duration = transcription.get('duration', 60)

                # 将结果包装成chunks格式
                chunks = [{
                    'audio': None,
                    'start': 0,
                    'end': duration,
                    'duration': duration,
                    'transcription': transcription
                }]
            else:
                # 模拟模式
                logger.warning("使用模拟模式处理音频")
                chunks = [{'audio': None, 'start': 0, 'end': 60, 'duration': 60}]
                duration = 60

            # 2. 处理转录结果
            all_results = []
            ground_crew_results = []

            for i, chunk in enumerate(chunks):
                logger.info(f"处理第 {i+1}/{len(chunks)} 个音频片段")

                if 'transcription' in chunk:
                    # 使用已有的转录结果
                    transcription = chunk['transcription']

                    # 处理Whisper返回的segments
                    if 'segments' in transcription:
                        for j, segment in enumerate(transcription['segments']):
                            segment_text = segment['text'].strip()
                            if segment_text:
                                # 判断是否为地勤语音
                                is_ground_crew = self.ground_crew_filter.is_ground_crew_speech(segment_text)
                                confidence = self.ground_crew_filter.calculate_confidence(segment_text)

                                result = {
                                    'segment_id': f"{i}_{j}",
                                    'start': segment['start'],
                                    'end': segment['end'],
                                    'duration': segment['end'] - segment['start'],
                                    'text': segment_text,
                                    'language': transcription.get('language', 'zh'),
                                    'language_probability': transcription.get('language_probability', 0.9),
                                    'is_ground_crew': is_ground_crew,
                                    'ground_crew_confidence': confidence,
                                    'avg_logprob': segment.get('avg_logprob', 0),
                                    'no_speech_prob': segment.get('no_speech_prob', 0),
                                    'words': segment.get('words', [])
                                }

                                all_results.append(result)

                                if is_ground_crew:
                                    ground_crew_results.append(result)
                                    logger.info(f"检测到地勤语音: {segment_text[:50]}...")
                    else:
                        # 处理整体转录结果
                        full_text = transcription.get('text', '').strip()
                        if full_text:
                            is_ground_crew = self.ground_crew_filter.is_ground_crew_speech(full_text)
                            confidence = self.ground_crew_filter.calculate_confidence(full_text)

                            result = {
                                'segment_id': i,
                                'start': chunk['start'],
                                'end': chunk['end'],
                                'duration': chunk['duration'],
                                'text': full_text,
                                'language': transcription.get('language', 'zh'),
                                'language_probability': transcription.get('language_probability', 0.9),
                                'is_ground_crew': is_ground_crew,
                                'ground_crew_confidence': confidence,
                                'words': transcription.get('words', [])
                            }

                            all_results.append(result)

                            if is_ground_crew:
                                ground_crew_results.append(result)
                                logger.info(f"检测到地勤语音: {full_text[:50]}...")
                else:
                    # 模拟转录（当没有真实转录时）
                    mock_text = f"模拟转录片段 {i+1}"
                    result = {
                        'segment_id': i,
                        'start': chunk['start'],
                        'end': chunk['end'],
                        'duration': chunk['duration'],
                        'text': mock_text,
                        'language': 'zh',
                        'language_probability': 0.9,
                        'is_ground_crew': False,
                        'ground_crew_confidence': 0.0,
                        'words': []
                    }
                    all_results.append(result)

            # 3. 整理最终结果
            processing_time = (datetime.now() - start_time).total_seconds()

            final_result = {
                'system_info': {
                    'version': '真实ASR系统 v1.0',
                    'whisper_model': self.asr_engine.model_size,
                    'processing_time': processing_time,
                    'timestamp': datetime.now().isoformat()
                },
                'audio_info': {
                    'file_path': audio_path,
                    'duration': duration,
                    'chunks_processed': len(chunks),
                    'file_size_mb': os.path.getsize(audio_path) / (1024*1024)
                },
                'transcription_results': {
                    'total_segments': len(all_results),
                    'ground_crew_segments': len(ground_crew_results),
                    'all_transcriptions': all_results,
                    'ground_crew_only': ground_crew_results
                },
                'summary': {
                    'ground_crew_text': ' '.join([r['text'] for r in ground_crew_results]),
                    'total_ground_crew_duration': sum([r['duration'] for r in ground_crew_results]),
                    'average_confidence': np.mean([r['ground_crew_confidence'] for r in ground_crew_results]) if ground_crew_results else 0,
                    'languages_detected': list(set([r['language'] for r in all_results]))
                }
            }

            logger.info(f"处理完成，耗时: {processing_time:.2f}秒")
            logger.info(f"检测到 {len(ground_crew_results)} 个地勤语音片段")

            return final_result

        except Exception as e:
            logger.error(f"处理音频失败: {e}")
            raise

def main():
    """主函数"""
    print("=" * 60)
    print("机场地勤ASR系统 - 真实版本")
    print("=" * 60)

    # 检查音频文件
    audio_file = "机场地勤音频.WAV"
    if not os.path.exists(audio_file):
        print(f"错误: 音频文件不存在 - {audio_file}")
        return

    try:
        # 创建ASR系统
        asr_system = RealASRSystem(whisper_model="base")  # 使用base模型，速度较快

        # 初始化系统
        if not asr_system.initialize():
            print("错误: ASR系统初始化失败")
            return

        # 处理音频
        result = asr_system.process_audio(audio_file)

        # 显示结果
        print(f"\n处理结果:")
        print(f"音频文件: {result['audio_info']['file_path']}")
        print(f"文件大小: {result['audio_info']['file_size_mb']:.2f} MB")
        print(f"音频时长: {result['audio_info']['duration']:.2f} 秒")
        print(f"处理时间: {result['system_info']['processing_time']:.2f} 秒")
        print(f"使用模型: {result['system_info']['whisper_model']}")
        print(f"总转录片段: {result['transcription_results']['total_segments']}")
        print(f"地勤语音片段: {result['transcription_results']['ground_crew_segments']}")

        if result['transcription_results']['ground_crew_segments'] > 0:
            print(f"地勤语音总时长: {result['summary']['total_ground_crew_duration']:.2f} 秒")
            print(f"平均置信度: {result['summary']['average_confidence']:.2f}")
            print(f"检测到的语言: {', '.join(result['summary']['languages_detected'])}")

            print(f"\n地勤人员语音转录结果:")
            print("-" * 50)
            for i, segment in enumerate(result['transcription_results']['ground_crew_only'], 1):
                print(f"{i}. [{segment['start']:.1f}s-{segment['end']:.1f}s] {segment['text']}")
                print(f"   置信度: {segment['ground_crew_confidence']:.2f}")
            print("-" * 50)

            print(f"\n完整地勤语音文本:")
            print(result['summary']['ground_crew_text'])
        else:
            print("\n未检测到地勤相关语音内容")

        # 保存结果
        output_file = f"real_asr_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        print(f"\n详细结果已保存到: {output_file}")

    except Exception as e:
        print(f"错误: {e}")
        logger.error(f"程序执行失败: {e}")

if __name__ == "__main__":
    main()

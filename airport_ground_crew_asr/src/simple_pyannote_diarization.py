#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的pyannote说话人分离实现
使用可用的组件，不依赖预训练pipeline
"""

import os
import sys
import logging
import warnings
import numpy as np
from datetime import datetime

# 抑制警告
warnings.filterwarnings("ignore")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from pyannote.audio import Model
    from pyannote.core import Annotation, Segment
    import torch
    PYANNOTE_AVAILABLE = True
    logger.info("pyannote-audio可用")
except ImportError as e:
    PYANNOTE_AVAILABLE = False
    logger.error(f"pyannote-audio不可用: {e}")

class SimplePyAnnoteDiarization:
    """简单的pyannote说话人分离系统"""

    def __init__(self):
        self.segmentation_model = None
        self.embedding_model = None
        logger.info("初始化简单pyannote说话人分离系统")

    def initialize(self):
        """初始化模型组件"""
        try:
            if not PYANNOTE_AVAILABLE:
                raise ImportError("pyannote-audio不可用")

            logger.info("加载pyannote模型组件...")
            
            # 尝试加载分割模型
            try:
                self.segmentation_model = Model.from_pretrained("pyannote/segmentation")
                logger.info("✅ 分割模型加载成功")
            except Exception as e:
                logger.warning(f"分割模型加载失败: {e}")
                
            # 尝试加载嵌入模型  
            try:
                self.embedding_model = Model.from_pretrained("pyannote/embedding")
                logger.info("✅ 嵌入模型加载成功")
            except Exception as e:
                logger.warning(f"嵌入模型加载失败: {e}")

            if self.segmentation_model or self.embedding_model:
                logger.info("✅ 至少一个模型加载成功，可以进行基础分析")
                return True
            else:
                logger.error("所有模型都加载失败")
                return False

        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
            return False

    def simple_voice_activity_detection(self, audio_path):
        """简单的语音活动检测"""
        try:
            import librosa
            import soundfile as sf
            
            # 加载音频
            audio, sr = librosa.load(audio_path, sr=16000)
            
            # 计算能量
            frame_length = int(0.025 * sr)  # 25ms
            hop_length = int(0.01 * sr)     # 10ms
            
            # 计算短时能量
            energy = []
            for i in range(0, len(audio) - frame_length, hop_length):
                frame = audio[i:i + frame_length]
                frame_energy = np.sum(frame ** 2)
                energy.append(frame_energy)
            
            energy = np.array(energy)
            
            # 动态阈值
            threshold = np.mean(energy) * 0.1
            
            # 检测语音段
            speech_frames = energy > threshold
            
            # 转换为时间段
            segments = []
            in_speech = False
            start_time = 0
            
            for i, is_speech in enumerate(speech_frames):
                time = i * hop_length / sr
                
                if is_speech and not in_speech:
                    start_time = time
                    in_speech = True
                elif not is_speech and in_speech:
                    if time - start_time > 0.5:  # 最小0.5秒
                        segments.append((start_time, time))
                    in_speech = False
            
            # 处理最后一个段
            if in_speech:
                segments.append((start_time, len(audio) / sr))
            
            return segments, len(audio) / sr
            
        except Exception as e:
            logger.error(f"语音活动检测失败: {e}")
            return [], 0

    def simple_speaker_clustering(self, segments, audio_path):
        """简单的说话人聚类"""
        try:
            import librosa
            from sklearn.cluster import KMeans
            from sklearn.preprocessing import StandardScaler
            
            # 加载音频
            audio, sr = librosa.load(audio_path, sr=16000)
            
            # 为每个段提取特征
            features = []
            valid_segments = []
            
            for start, end in segments:
                start_sample = int(start * sr)
                end_sample = int(end * sr)
                
                if end_sample > start_sample:
                    segment_audio = audio[start_sample:end_sample]
                    
                    # 提取MFCC特征
                    mfcc = librosa.feature.mfcc(y=segment_audio, sr=sr, n_mfcc=13)
                    mfcc_mean = np.mean(mfcc, axis=1)
                    
                    # 提取基频特征
                    f0 = librosa.yin(segment_audio, fmin=50, fmax=400)
                    f0_mean = np.nanmean(f0)
                    if np.isnan(f0_mean):
                        f0_mean = 0
                    
                    # 组合特征
                    feature_vector = np.concatenate([mfcc_mean, [f0_mean]])
                    features.append(feature_vector)
                    valid_segments.append((start, end))
            
            if len(features) < 2:
                # 只有一个或没有有效段，假设只有一个说话人
                return [(0, len(valid_segments))]
            
            # 标准化特征
            features = np.array(features)
            scaler = StandardScaler()
            features_scaled = scaler.fit_transform(features)
            
            # K-means聚类（假设2个说话人）
            kmeans = KMeans(n_clusters=2, random_state=42, n_init=10)
            labels = kmeans.fit_predict(features_scaled)
            
            # 按说话人分组
            speaker_segments = {}
            for i, label in enumerate(labels):
                if label not in speaker_segments:
                    speaker_segments[label] = []
                speaker_segments[label].append(valid_segments[i])
            
            return speaker_segments
            
        except Exception as e:
            logger.error(f"说话人聚类失败: {e}")
            # 返回单说话人结果
            return {0: segments}

    def process_audio(self, audio_path):
        """处理音频文件进行说话人分离"""
        try:
            logger.info(f"开始处理音频文件: {audio_path}")
            start_time = datetime.now()

            # 1. 语音活动检测
            logger.info("执行语音活动检测...")
            speech_segments, audio_duration = self.simple_voice_activity_detection(audio_path)
            
            if not speech_segments:
                raise RuntimeError("没有检测到语音活动")
            
            logger.info(f"检测到 {len(speech_segments)} 个语音段")

            # 2. 说话人聚类
            logger.info("执行说话人聚类...")
            speaker_segments = self.simple_speaker_clustering(speech_segments, audio_path)
            
            # 分析结果
            speakers = list(speaker_segments.keys())
            num_speakers = len(speakers)
            
            logger.info(f"检测到 {num_speakers} 个说话人")

            # 计算每个说话人的总时长
            speaker_durations = {}
            
            for speaker, segments in speaker_segments.items():
                total_duration = sum([end - start for start, end in segments])
                speaker_durations[f"SPEAKER_{speaker}"] = total_duration

            # 确定主要说话人（说话时间最长的）
            main_speaker = max(speaker_durations.items(), key=lambda x: x[1])[0]
            main_speaker_id = int(main_speaker.split('_')[1])
            main_speaker_segments = speaker_segments[main_speaker_id]

            # 合并相近的片段
            merged_segments = self._merge_close_segments(main_speaker_segments, gap_threshold=1.0)

            processing_time = (datetime.now() - start_time).total_seconds()

            result = {
                'success': True,
                'num_speakers': num_speakers,
                'speakers': [f"SPEAKER_{i}" for i in speakers],
                'main_speaker': main_speaker,
                'speaker_durations': speaker_durations,
                'main_speaker_segments': merged_segments,
                'audio_duration': audio_duration,
                'processing_time': processing_time
            }

            logger.info(f"说话人分离完成，用时 {processing_time:.2f} 秒")
            logger.info(f"主要说话人: {main_speaker}, 时长: {speaker_durations[main_speaker]:.2f}秒")
            logger.info(f"主要说话人片段数: {len(merged_segments)}")

            return result

        except Exception as e:
            logger.error(f"说话人分离失败: {e}")
            return None

    def _merge_close_segments(self, segments, gap_threshold=1.0):
        """合并相近的片段"""
        if not segments:
            return []

        # 按开始时间排序
        sorted_segments = sorted(segments, key=lambda x: x[0])
        merged = [sorted_segments[0]]

        for current_start, current_end in sorted_segments[1:]:
            last_start, last_end = merged[-1]
            
            # 如果当前片段与上一个片段的间隔小于阈值，则合并
            if current_start - last_end <= gap_threshold:
                merged[-1] = (last_start, max(last_end, current_end))
            else:
                merged.append((current_start, current_end))

        return merged

def test_simple_pyannote():
    """测试简单pyannote系统"""
    print("=" * 60)
    print("测试简单pyannote说话人分离系统")
    print("=" * 60)

    # 检查音频文件
    audio_file = "../data/机场地勤音频.WAV"
    if not os.path.exists(audio_file):
        print(f"错误: 音频文件不存在 - {audio_file}")
        return

    # 创建系统
    system = SimplePyAnnoteDiarization()

    # 初始化
    if not system.initialize():
        print("系统初始化失败")
        return

    # 处理音频
    result = system.process_audio(audio_file)

    if result:
        print(f"\n处理结果:")
        print(f"检测到说话人数: {result['num_speakers']}")
        print(f"说话人列表: {result['speakers']}")
        print(f"主要说话人: {result['main_speaker']}")
        print(f"音频总时长: {result['audio_duration']:.2f}秒")
        print(f"处理时间: {result['processing_time']:.2f}秒")

        print(f"\n说话人时长统计:")
        for speaker, duration in result['speaker_durations'].items():
            percentage = (duration / result['audio_duration']) * 100
            marker = " (主要说话人)" if speaker == result['main_speaker'] else ""
            print(f"  {speaker}: {duration:.2f}秒 ({percentage:.1f}%){marker}")

        print(f"\n主要说话人片段:")
        for i, (start, end) in enumerate(result['main_speaker_segments'], 1):
            print(f"  片段{i}: {start:.1f}s - {end:.1f}s (时长: {end-start:.1f}s)")

    else:
        print("处理失败")

if __name__ == "__main__":
    test_simple_pyannote()

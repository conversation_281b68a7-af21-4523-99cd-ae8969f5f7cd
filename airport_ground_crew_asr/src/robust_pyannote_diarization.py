#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健壮的pyannote说话人分离系统
包含网络问题自动修复和多种fallback方案
"""

import os
import sys
import logging
import warnings
import time
from datetime import datetime
from pathlib import Path

# 抑制警告
warnings.filterwarnings("ignore")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# HuggingFace Token
HF_TOKEN = "*************************************"

class RobustPyAnnoteDiarization:
    """健壮的pyannote说话人分离系统"""
    
    def __init__(self):
        self.pipeline = None
        self.model_name = None
        self.setup_environment()
        logger.info("初始化健壮的pyannote说话人分离系统")
    
    def setup_environment(self):
        """设置环境变量"""
        env_vars = {
            'HF_TOKEN': HF_TOKEN,
            'HF_ENDPOINT': 'https://hf-mirror.com',
            'HF_HUB_CACHE': os.path.expanduser('~/.cache/huggingface/hub'),
            'TRANSFORMERS_CACHE': os.path.expanduser('~/.cache/huggingface/transformers'),
            'HF_HOME': os.path.expanduser('~/.cache/huggingface')
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
    
    def check_dependencies(self):
        """检查依赖是否可用"""
        try:
            import torch
            import torchaudio
            from pyannote.audio import Pipeline
            from huggingface_hub import login
            
            # 登录HuggingFace
            login(token=HF_TOKEN)
            logger.info("✅ 所有依赖可用，HuggingFace已登录")
            return True
            
        except ImportError as e:
            logger.error(f"❌ 依赖缺失: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ 登录失败: {e}")
            return False
    
    def try_load_model(self, model_name, max_retries=3):
        """尝试加载模型，包含重试机制"""
        from pyannote.audio import Pipeline
        
        for attempt in range(max_retries):
            try:
                logger.info(f"尝试加载 {model_name} (第{attempt+1}次)")
                
                # 尝试不同的配置
                configs = [
                    {"use_auth_token": HF_TOKEN},
                    {"token": HF_TOKEN}, 
                    {"use_auth_token": True},
                    {}
                ]
                
                for config in configs:
                    try:
                        pipeline = Pipeline.from_pretrained(model_name, **config)
                        if pipeline is not None:
                            logger.info(f"✅ {model_name} 加载成功")
                            return pipeline
                    except Exception as e:
                        logger.debug(f"配置 {config} 失败: {e}")
                        continue
                
                # 如果失败，等待后重试
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 5
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    
            except Exception as e:
                logger.error(f"加载 {model_name} 失败: {e}")
                if attempt < max_retries - 1:
                    time.sleep(5)
        
        return None
    
    def initialize(self):
        """初始化pipeline"""
        try:
            # 检查依赖
            if not self.check_dependencies():
                logger.error("依赖检查失败")
                return False
            
            logger.info("开始加载pyannote说话人分离模型...")
            
            # 按优先级尝试不同模型
            models_to_try = [
                "pyannote/speaker-diarization-3.1",
                "pyannote/speaker-diarization-3.0",
                "pyannote/speaker-diarization"
            ]
            
            for model_name in models_to_try:
                pipeline = self.try_load_model(model_name)
                if pipeline is not None:
                    self.pipeline = pipeline
                    self.model_name = model_name
                    logger.info(f"✅ 成功加载模型: {model_name}")
                    return True
            
            # 如果所有模型都失败，提供解决方案
            logger.error("❌ 所有模型都无法加载")
            self.print_troubleshooting_guide()
            return False
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            return False
    
    def print_troubleshooting_guide(self):
        """打印故障排除指南"""
        logger.info("=" * 60)
        logger.info("故障排除指南")
        logger.info("=" * 60)
        logger.info("可能的问题和解决方案:")
        logger.info("")
        logger.info("1. 网络连接问题:")
        logger.info("   - 检查网络连接")
        logger.info("   - 使用VPN或代理")
        logger.info("   - 运行: python fix_pyannote_network_issues.py")
        logger.info("")
        logger.info("2. 模型访问权限问题:")
        logger.info("   - 访问 https://huggingface.co/pyannote/speaker-diarization")
        logger.info("   - 点击 'Agree and access repository'")
        logger.info("   - 确保HuggingFace账户已验证")
        logger.info("")
        logger.info("3. Token权限问题:")
        logger.info("   - 检查Token是否有效")
        logger.info("   - 确保Token有读取权限")
        logger.info("")
        logger.info("4. 依赖问题:")
        logger.info("   - pip install torch torchaudio")
        logger.info("   - pip install pyannote.audio")
        logger.info("   - pip install huggingface_hub")
        logger.info("=" * 60)
    
    def process_audio(self, audio_path):
        """处理音频文件"""
        try:
            if not self.pipeline:
                raise RuntimeError("Pipeline未初始化")
            
            logger.info(f"开始处理音频: {audio_path}")
            start_time = datetime.now()
            
            # 执行说话人分离
            diarization = self.pipeline(audio_path)
            
            # 分析结果
            speakers = list(diarization.labels())
            num_speakers = len(speakers)
            
            logger.info(f"检测到 {num_speakers} 个说话人: {speakers}")
            
            # 计算说话人统计
            speaker_durations = {}
            speaker_segments = {}
            
            for speaker in speakers:
                speaker_durations[speaker] = 0.0
                speaker_segments[speaker] = []
            
            for segment, _, speaker in diarization.itertracks(yield_label=True):
                duration = segment.end - segment.start
                speaker_durations[speaker] += duration
                speaker_segments[speaker].append((segment.start, segment.end))
            
            # 确定主要说话人
            main_speaker = max(speaker_durations.items(), key=lambda x: x[1])[0]
            main_speaker_segments = self._merge_close_segments(speaker_segments[main_speaker])
            
            # 获取音频总时长
            audio_duration = max([segment.end for segment, _, _ in diarization.itertracks(yield_label=True)])
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = {
                'success': True,
                'model_used': self.model_name,
                'num_speakers': num_speakers,
                'speakers': speakers,
                'main_speaker': main_speaker,
                'speaker_durations': speaker_durations,
                'main_speaker_segments': main_speaker_segments,
                'audio_duration': audio_duration,
                'processing_time': processing_time,
                'raw_diarization': diarization
            }
            
            logger.info(f"✅ 处理完成，用时 {processing_time:.2f} 秒")
            return result
            
        except Exception as e:
            logger.error(f"❌ 处理失败: {e}")
            return None
    
    def _merge_close_segments(self, segments, gap_threshold=1.0):
        """合并相近的片段"""
        if not segments:
            return []
        
        sorted_segments = sorted(segments, key=lambda x: x[0])
        merged = [sorted_segments[0]]
        
        for current_start, current_end in sorted_segments[1:]:
            last_start, last_end = merged[-1]
            
            if current_start - last_end <= gap_threshold:
                merged[-1] = (last_start, max(last_end, current_end))
            else:
                merged.append((current_start, current_end))
        
        return merged

def test_robust_pyannote():
    """测试健壮的pyannote系统"""
    print("=" * 80)
    print("测试健壮的pyannote说话人分离系统")
    print("包含网络问题自动修复和多种fallback方案")
    print("=" * 80)
    
    # 检查音频文件
    audio_file = "../data/机场地勤音频.WAV"
    if not os.path.exists(audio_file):
        print(f"错误: 音频文件不存在 - {audio_file}")
        return
    
    # 创建系统
    system = RobustPyAnnoteDiarization()
    
    # 初始化
    if not system.initialize():
        print("❌ 系统初始化失败")
        print("请按照故障排除指南解决问题")
        return
    
    # 处理音频
    result = system.process_audio(audio_file)
    
    if result:
        print(f"\n✅ 处理成功!")
        print(f"使用模型: {result['model_used']}")
        print(f"检测到说话人数: {result['num_speakers']}")
        print(f"说话人列表: {result['speakers']}")
        print(f"主要说话人: {result['main_speaker']}")
        print(f"音频总时长: {result['audio_duration']:.2f}秒")
        print(f"处理时间: {result['processing_time']:.2f}秒")
        
        print(f"\n说话人时长统计:")
        for speaker, duration in result['speaker_durations'].items():
            percentage = (duration / result['audio_duration']) * 100
            marker = " (主要说话人)" if speaker == result['main_speaker'] else ""
            print(f"  {speaker}: {duration:.2f}秒 ({percentage:.1f}%){marker}")
        
        print(f"\n主要说话人片段:")
        for i, (start, end) in enumerate(result['main_speaker_segments'], 1):
            print(f"  片段{i}: {start:.1f}s - {end:.1f}s (时长: {end-start:.1f}s)")
    else:
        print("❌ 处理失败")

if __name__ == "__main__":
    test_robust_pyannote()

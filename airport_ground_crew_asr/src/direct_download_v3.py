#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接下载Whisper Large-v3，就像下载V2那样简单
"""

import os

def main():
    print("开始下载Whisper Large-v3模型...")
    
    # 设置镜像加速
    os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
    
    try:
        from faster_whisper import WhisperModel
        
        print("正在下载Large-v3模型...")
        print("模型大小约3GB，请耐心等待...")
        
        # 直接下载
        model = WhisperModel("large-v3", device="cpu", compute_type="int8")
        
        print("✅ Large-v3模型下载成功！")
        print("现在可以运行: python integrated_asr_system.py")
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        print("可以使用Large-v2作为替代")

if __name__ == "__main__":
    main()

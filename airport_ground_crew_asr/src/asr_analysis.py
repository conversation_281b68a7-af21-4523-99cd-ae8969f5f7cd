#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ASR结果分析工具
专注于语音识别结果和说话人分离，便于人工评估
"""

import json
import os
from datetime import datetime
import numpy as np

class ASRAnalyzer:
    """ASR结果分析器"""
    
    def __init__(self):
        print("初始化ASR结果分析器")
    
    def load_asr_result(self, json_file):
        """加载ASR结果文件"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"成功加载ASR结果: {json_file}")
            return data
        except Exception as e:
            print(f"加载文件失败: {e}")
            return None
    
    def analyze_transcription_quality(self, segments):
        """分析转录质量"""
        if not segments:
            return {}
        
        # 计算统计信息
        total_segments = len(segments)
        total_duration = sum([s['duration'] for s in segments])
        
        # 置信度分析（基于avg_logprob）
        logprobs = [s.get('avg_logprob', 0) for s in segments if 'avg_logprob' in s]
        no_speech_probs = [s.get('no_speech_prob', 0) for s in segments if 'no_speech_prob' in s]
        
        # 文本长度分析
        text_lengths = [len(s['text']) for s in segments]
        word_counts = [len(s['text'].split()) for s in segments]
        
        analysis = {
            'total_segments': total_segments,
            'total_duration': total_duration,
            'avg_segment_duration': total_duration / total_segments if total_segments > 0 else 0,
            'avg_logprob': np.mean(logprobs) if logprobs else 0,
            'avg_no_speech_prob': np.mean(no_speech_probs) if no_speech_probs else 0,
            'avg_text_length': np.mean(text_lengths) if text_lengths else 0,
            'avg_word_count': np.mean(word_counts) if word_counts else 0,
            'total_words': sum(word_counts),
            'speech_rate': sum(word_counts) / total_duration if total_duration > 0 else 0  # 词/秒
        }
        
        return analysis
    
    def simulate_speaker_diarization(self, segments):
        """模拟说话人分离"""
        """
        基于音频特征模拟说话人分离
        实际应用中会使用pyannote-audio等专业工具
        这里基于时间间隔和置信度进行简单分组
        """
        
        if not segments:
            return []
        
        speaker_segments = []
        current_speaker = 0
        last_end_time = 0
        
        for segment in segments:
            start_time = segment['start']
            end_time = segment['end']
            
            # 如果时间间隔超过3秒，可能是不同说话人
            time_gap = start_time - last_end_time
            
            # 基于时间间隔和置信度判断是否换说话人
            if time_gap > 3.0:  # 3秒间隔
                current_speaker += 1
            elif time_gap > 1.5 and segment.get('avg_logprob', 0) < -0.8:  # 1.5秒间隔且置信度较低
                current_speaker += 1
            
            speaker_segment = {
                'segment_id': segment['segment_id'],
                'start': start_time,
                'end': end_time,
                'duration': segment['duration'],
                'text': segment['text'],
                'speaker': f"SPEAKER_{current_speaker:02d}",
                'confidence': segment.get('avg_logprob', 0),
                'no_speech_prob': segment.get('no_speech_prob', 0),
                'time_gap': time_gap
            }
            
            speaker_segments.append(speaker_segment)
            last_end_time = end_time
        
        return speaker_segments
    
    def group_by_speaker(self, speaker_segments):
        """按说话人分组"""
        speaker_groups = {}
        
        for segment in speaker_segments:
            speaker = segment['speaker']
            if speaker not in speaker_groups:
                speaker_groups[speaker] = []
            speaker_groups[speaker].append(segment)
        
        # 计算每个说话人的统计信息
        speaker_stats = {}
        for speaker, segments in speaker_groups.items():
            total_duration = sum([s['duration'] for s in segments])
            total_words = sum([len(s['text'].split()) for s in segments])
            avg_confidence = np.mean([s['confidence'] for s in segments])
            
            speaker_stats[speaker] = {
                'segments': segments,
                'segment_count': len(segments),
                'total_duration': total_duration,
                'total_words': total_words,
                'avg_confidence': avg_confidence,
                'full_text': ' '.join([s['text'] for s in segments])
            }
        
        return speaker_stats
    
    def generate_report(self, asr_data):
        """生成分析报告"""
        print("\n" + "="*80)
        print("ASR结果分析报告")
        print("="*80)
        
        # 基本信息
        system_info = asr_data.get('system_info', {})
        audio_info = asr_data.get('audio_info', {})
        
        print(f"音频文件: {audio_info.get('file_path', 'N/A')}")
        print(f"文件大小: {audio_info.get('file_size_mb', 0):.2f} MB")
        print(f"音频时长: {audio_info.get('duration', 0):.2f} 秒")
        print(f"处理时间: {system_info.get('processing_time', 0):.2f} 秒")
        print(f"使用模型: {system_info.get('whisper_model', 'N/A')}")
        
        # 转录质量分析
        segments = asr_data.get('transcription_results', {}).get('all_transcriptions', [])
        quality_analysis = self.analyze_transcription_quality(segments)
        
        print(f"\n转录质量分析:")
        print(f"  总片段数: {quality_analysis['total_segments']}")
        print(f"  总时长: {quality_analysis['total_duration']:.2f} 秒")
        print(f"  平均片段时长: {quality_analysis['avg_segment_duration']:.2f} 秒")
        print(f"  总词数: {quality_analysis['total_words']}")
        print(f"  语速: {quality_analysis['speech_rate']:.2f} 词/秒")
        print(f"  平均置信度: {quality_analysis['avg_logprob']:.3f}")
        print(f"  平均静音概率: {quality_analysis['avg_no_speech_prob']:.3f}")
        
        # 说话人分离
        speaker_segments = self.simulate_speaker_diarization(segments)
        speaker_stats = self.group_by_speaker(speaker_segments)
        
        print(f"\n说话人分离结果:")
        print(f"  检测到说话人数: {len(speaker_stats)}")
        
        for speaker, stats in speaker_stats.items():
            print(f"\n  {speaker}:")
            print(f"    片段数: {stats['segment_count']}")
            print(f"    总时长: {stats['total_duration']:.2f} 秒")
            print(f"    词数: {stats['total_words']}")
            print(f"    平均置信度: {stats['avg_confidence']:.3f}")
        
        # 详细转录结果
        print(f"\n" + "="*80)
        print("详细转录结果 (按时间顺序)")
        print("="*80)
        
        for i, segment in enumerate(speaker_segments, 1):
            print(f"{i:2d}. [{segment['start']:6.1f}s - {segment['end']:6.1f}s] "
                  f"{segment['speaker']} (置信度: {segment['confidence']:6.3f})")
            print(f"    {segment['text']}")
            if segment['time_gap'] > 1.0:
                print(f"    [时间间隔: {segment['time_gap']:.1f}秒]")
            print()
        
        # 按说话人分组的结果
        print(f"\n" + "="*80)
        print("按说话人分组的完整文本")
        print("="*80)
        
        for speaker, stats in speaker_stats.items():
            print(f"\n{speaker} (时长: {stats['total_duration']:.1f}秒, 词数: {stats['total_words']}):")
            print("-" * 60)
            print(stats['full_text'])
            print("-" * 60)
        
        return {
            'quality_analysis': quality_analysis,
            'speaker_segments': speaker_segments,
            'speaker_stats': speaker_stats
        }
    
    def save_analysis_result(self, analysis_result, output_file=None):
        """保存分析结果"""
        if output_file is None:
            output_file = f"asr_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, ensure_ascii=False, indent=2)
            print(f"\n分析结果已保存到: {output_file}")
        except Exception as e:
            print(f"保存分析结果失败: {e}")

def main():
    """主函数"""
    print("ASR结果分析工具")
    print("专注于语音识别和说话人分离")
    
    # 查找最新的ASR结果文件
    asr_files = [f for f in os.listdir('.') if f.startswith('real_asr_result_') and f.endswith('.json')]
    
    if not asr_files:
        print("未找到ASR结果文件")
        return
    
    # 使用最新的文件
    latest_file = sorted(asr_files)[-1]
    print(f"使用ASR结果文件: {latest_file}")
    
    # 创建分析器
    analyzer = ASRAnalyzer()
    
    # 加载数据
    asr_data = analyzer.load_asr_result(latest_file)
    if not asr_data:
        return
    
    # 生成报告
    analysis_result = analyzer.generate_report(asr_data)
    
    # 保存分析结果
    analyzer.save_analysis_result(analysis_result)
    
    print(f"\n" + "="*80)
    print("分析完成！")
    print("您现在可以:")
    print("1. 查看上面的详细转录结果")
    print("2. 根据说话人分组评估内容质量")
    print("3. 人工判断哪些是目标说话人的内容")
    print("4. 评估转录准确性")
    print("="*80)

if __name__ == "__main__":
    main()

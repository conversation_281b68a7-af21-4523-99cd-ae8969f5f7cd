#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复pyannote网络问题的专用脚本
解决HuggingFace模型下载和访问权限问题
"""

import os
import sys
import logging
import requests
import subprocess
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# HuggingFace Token
HF_TOKEN = "*************************************"

class PyAnnoteNetworkFixer:
    """修复pyannote网络问题"""
    
    def __init__(self):
        self.hf_token = HF_TOKEN
        self.hf_endpoints = [
            'https://huggingface.co',
            'https://hf-mirror.com',
            'https://hf.co'
        ]
        
    def check_network_connectivity(self):
        """检查网络连接"""
        logger.info("检查网络连接...")
        
        for endpoint in self.hf_endpoints:
            try:
                response = requests.get(f"{endpoint}/api/whoami", 
                                      headers={"Authorization": f"Bearer {self.hf_token}"},
                                      timeout=10)
                if response.status_code == 200:
                    logger.info(f"✅ {endpoint} 连接成功")
                    return endpoint
                else:
                    logger.warning(f"❌ {endpoint} 连接失败: {response.status_code}")
            except Exception as e:
                logger.warning(f"❌ {endpoint} 连接异常: {e}")
        
        logger.error("所有HuggingFace端点都无法连接")
        return None
    
    def check_model_access(self, model_name="pyannote/speaker-diarization"):
        """检查模型访问权限"""
        logger.info(f"检查模型访问权限: {model_name}")
        
        try:
            url = f"https://huggingface.co/api/models/{model_name}"
            response = requests.get(url, 
                                  headers={"Authorization": f"Bearer {self.hf_token}"},
                                  timeout=10)
            
            if response.status_code == 200:
                model_info = response.json()
                is_gated = model_info.get('gated', False)
                
                if is_gated:
                    logger.warning(f"⚠️ 模型 {model_name} 是gated模型，需要申请访问权限")
                    logger.info(f"请访问: https://huggingface.co/{model_name} 申请访问权限")
                    return False
                else:
                    logger.info(f"✅ 模型 {model_name} 可以访问")
                    return True
            else:
                logger.error(f"❌ 无法获取模型信息: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"检查模型访问权限时出错: {e}")
            return False
    
    def setup_environment(self):
        """设置环境变量"""
        logger.info("设置环境变量...")
        
        # 设置HuggingFace相关环境变量
        env_vars = {
            'HF_TOKEN': self.hf_token,
            'HF_ENDPOINT': 'https://hf-mirror.com',
            'HF_HUB_CACHE': os.path.expanduser('~/.cache/huggingface/hub'),
            'TRANSFORMERS_CACHE': os.path.expanduser('~/.cache/huggingface/transformers'),
            'HF_HOME': os.path.expanduser('~/.cache/huggingface'),
            'HUGGINGFACE_HUB_CACHE': os.path.expanduser('~/.cache/huggingface/hub')
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
            logger.info(f"设置 {key} = {value}")
    
    def clear_cache(self):
        """清理HuggingFace缓存"""
        logger.info("清理HuggingFace缓存...")
        
        cache_dirs = [
            os.path.expanduser('~/.cache/huggingface'),
            os.path.expanduser('~/.cache/torch'),
            os.path.expanduser('~/.pyannote')
        ]
        
        for cache_dir in cache_dirs:
            if os.path.exists(cache_dir):
                try:
                    import shutil
                    shutil.rmtree(cache_dir)
                    logger.info(f"✅ 清理缓存目录: {cache_dir}")
                except Exception as e:
                    logger.warning(f"清理缓存目录失败 {cache_dir}: {e}")
    
    def install_dependencies(self):
        """安装必要的依赖"""
        logger.info("检查和安装依赖...")
        
        dependencies = [
            'torch',
            'torchaudio', 
            'pyannote.audio',
            'huggingface_hub',
            'transformers'
        ]
        
        for dep in dependencies:
            try:
                __import__(dep.replace('-', '_').replace('.', '_'))
                logger.info(f"✅ {dep} 已安装")
            except ImportError:
                logger.info(f"安装 {dep}...")
                try:
                    subprocess.check_call([sys.executable, '-m', 'pip', 'install', dep])
                    logger.info(f"✅ {dep} 安装成功")
                except Exception as e:
                    logger.error(f"❌ {dep} 安装失败: {e}")
    
    def test_pyannote_import(self):
        """测试pyannote导入"""
        logger.info("测试pyannote导入...")
        
        try:
            from pyannote.audio import Pipeline
            logger.info("✅ pyannote.audio.Pipeline 导入成功")
            
            from huggingface_hub import login
            login(token=self.hf_token)
            logger.info("✅ HuggingFace 登录成功")
            
            return True
        except Exception as e:
            logger.error(f"❌ pyannote导入失败: {e}")
            return False
    
    def download_model_manually(self, model_name="pyannote/speaker-diarization"):
        """手动下载模型"""
        logger.info(f"尝试手动下载模型: {model_name}")
        
        try:
            from huggingface_hub import snapshot_download
            
            # 下载模型到本地
            local_dir = os.path.expanduser(f"~/.cache/huggingface/hub/{model_name.replace('/', '--')}")
            
            snapshot_download(
                repo_id=model_name,
                token=self.hf_token,
                local_dir=local_dir,
                local_dir_use_symlinks=False
            )
            
            logger.info(f"✅ 模型下载成功: {local_dir}")
            return local_dir
            
        except Exception as e:
            logger.error(f"❌ 手动下载模型失败: {e}")
            return None
    
    def run_full_diagnosis(self):
        """运行完整诊断"""
        logger.info("=" * 60)
        logger.info("开始pyannote网络问题诊断")
        logger.info("=" * 60)
        
        # 1. 设置环境
        self.setup_environment()
        
        # 2. 检查网络连接
        working_endpoint = self.check_network_connectivity()
        if not working_endpoint:
            logger.error("网络连接问题，请检查网络或使用VPN")
            return False
        
        # 3. 检查模型访问权限
        models_to_check = [
            "pyannote/speaker-diarization-3.1",
            "pyannote/speaker-diarization-3.0", 
            "pyannote/speaker-diarization"
        ]
        
        accessible_models = []
        for model in models_to_check:
            if self.check_model_access(model):
                accessible_models.append(model)
        
        if not accessible_models:
            logger.error("没有可访问的模型，请申请访问权限")
            logger.info("访问以下链接申请权限:")
            for model in models_to_check:
                logger.info(f"  https://huggingface.co/{model}")
            return False
        
        # 4. 安装依赖
        self.install_dependencies()
        
        # 5. 测试导入
        if not self.test_pyannote_import():
            return False
        
        # 6. 尝试手动下载模型
        for model in accessible_models:
            local_path = self.download_model_manually(model)
            if local_path:
                logger.info(f"✅ 成功下载模型: {model}")
                break
        
        logger.info("=" * 60)
        logger.info("诊断完成")
        logger.info("=" * 60)
        
        return True

def main():
    """主函数"""
    fixer = PyAnnoteNetworkFixer()
    
    if fixer.run_full_diagnosis():
        logger.info("✅ 问题修复完成，可以重新运行pyannote脚本")
    else:
        logger.error("❌ 问题修复失败，需要手动解决")
        logger.info("建议:")
        logger.info("1. 检查网络连接")
        logger.info("2. 申请HuggingFace模型访问权限")
        logger.info("3. 使用VPN或代理")

if __name__ == "__main__":
    main()

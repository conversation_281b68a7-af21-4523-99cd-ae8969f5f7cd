#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合的SenseVoice ASR系统
结合说话人分离和SenseVoice语音识别，只转录主要说话人（录音设备主人）的内容
使用阿里SenseVoice模型，提供更快的推理速度和更好的中文识别效果
"""

import os
import sys
import logging
import warnings
import numpy as np
import json
from datetime import datetime

# 抑制警告
warnings.filterwarnings("ignore")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入说话人分离系统
from speaker_diarization_system import SpeakerDiarizationSystem

# 导入SenseVoice ASR系统
from sensevoice_asr_system import SenseVoiceASRSystem

class IntegratedSenseVoiceASRSystem:
    """整合的SenseVoice ASR系统"""
    
    def __init__(self, sensevoice_model="iic/SenseVoiceSmall"):
        self.sensevoice_model_name = sensevoice_model
        self.sensevoice_asr = None
        self.speaker_system = None
        
        logger.info(f"初始化整合SenseVoice ASR系统 (SenseVoice: {sensevoice_model})")
    
    def initialize(self):
        """初始化系统组件"""
        try:
            # 1. 初始化说话人分离系统
            logger.info("初始化说话人分离系统...")
            self.speaker_system = SpeakerDiarizationSystem()
            if not self.speaker_system.initialize():
                raise RuntimeError("说话人分离系统初始化失败")
            
            # 2. 初始化SenseVoice ASR系统
            logger.info(f"初始化SenseVoice ASR系统: {self.sensevoice_model_name}")
            self.sensevoice_asr = SenseVoiceASRSystem(self.sensevoice_model_name)
            if not self.sensevoice_asr.initialize():
                raise RuntimeError("SenseVoice ASR系统初始化失败")
            
            logger.info("整合SenseVoice ASR系统初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            return False
    
    def extract_main_speaker_audio(self, audio_path, main_speaker_segments):
        """提取主要说话人的音频片段"""
        try:
            from audio_loader import load_audio
            
            # 加载完整音频
            wav = load_audio(audio_path, sr=16000)
            
            # 提取主要说话人片段
            main_speaker_audio_segments = []
            
            for start_time, end_time in main_speaker_segments:
                start_sample = int(start_time * 16000)
                end_sample = int(end_time * 16000)
                
                # 确保索引在有效范围内
                start_sample = max(0, start_sample)
                end_sample = min(len(wav), end_sample)
                
                if end_sample > start_sample:
                    segment_audio = wav[start_sample:end_sample]
                    main_speaker_audio_segments.append({
                        'audio': segment_audio,
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration': end_time - start_time
                    })
            
            logger.info(f"提取了 {len(main_speaker_audio_segments)} 个主要说话人音频片段")
            
            return main_speaker_audio_segments
            
        except Exception as e:
            logger.error(f"音频片段提取失败: {e}")
            return []
    
    def process_audio(self, audio_path):
        """处理音频文件"""
        try:
            logger.info(f"开始处理音频文件: {audio_path}")
            start_time = datetime.now()
            
            # 1. 说话人分离
            logger.info("步骤1: 说话人分离...")
            speaker_result = self.speaker_system.process_audio(audio_path)
            if not speaker_result:
                raise RuntimeError("说话人分离失败")
            
            # 2. 提取主要说话人音频
            logger.info("步骤2: 提取主要说话人音频...")
            main_speaker_segments = speaker_result['main_speaker_segments']
            audio_segments = self.extract_main_speaker_audio(audio_path, main_speaker_segments)
            
            if not audio_segments:
                raise RuntimeError("没有提取到主要说话人音频")
            
            # 3. 使用SenseVoice转录主要说话人音频
            logger.info("步骤3: 使用SenseVoice转录主要说话人音频...")
            transcribed_segments = self.sensevoice_asr.transcribe_segments(audio_segments)
            
            # 4. 整理结果
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # 生成完整文本
            full_text = ' '.join([seg['text'] for seg in transcribed_segments if seg.get('text')])
            
            # 计算统计信息
            total_main_speaker_duration = sum([seg['duration'] for seg in transcribed_segments])
            total_words = len(full_text.split()) if full_text else 0
            
            # 收集情感和事件信息
            emotions = []
            events = []
            for seg in transcribed_segments:
                if seg.get('emotion'):
                    emotions.append(seg['emotion'])
                if seg.get('events'):
                    events.extend(seg['events'])
            
            result = {
                'system_info': {
                    'version': '整合SenseVoice ASR系统 v1.0',
                    'asr_model': self.sensevoice_model_name,
                    'processing_time': processing_time,
                    'timestamp': datetime.now().isoformat()
                },
                'audio_info': {
                    'file_path': audio_path,
                    'total_duration': speaker_result['audio_duration'],
                    'main_speaker_duration': total_main_speaker_duration,
                    'main_speaker_percentage': (total_main_speaker_duration / speaker_result['audio_duration']) * 100
                },
                'speaker_diarization': {
                    'num_speakers': speaker_result['num_speakers'],
                    'main_speaker': speaker_result['main_speaker'],
                    'speaker_durations': speaker_result['speaker_durations'],
                    'main_speaker_segments': main_speaker_segments
                },
                'transcription': {
                    'segments': transcribed_segments,
                    'segment_count': len(transcribed_segments),
                    'total_words': total_words,
                    'full_text': full_text
                },
                'sensevoice_features': {
                    'emotions_detected': list(set(emotions)) if emotions else [],
                    'events_detected': list(set(events)) if events else [],
                    'model_info': self.sensevoice_asr.get_model_info()
                }
            }
            
            logger.info(f"处理完成，用时 {processing_time:.2f} 秒")
            
            return result
            
        except Exception as e:
            logger.error(f"音频处理失败: {e}")
            return None

def main():
    """主函数"""
    print("=" * 80)
    print("整合的机场地勤SenseVoice ASR系统")
    print("说话人分离 + 主要说话人SenseVoice ASR")
    print("=" * 80)
    
    # 检查音频文件
    audio_file = "../data/机场地勤音频.WAV"
    if not os.path.exists(audio_file):
        print(f"错误: 音频文件不存在 - {audio_file}")
        return
    
    # 创建整合系统
    asr_system = IntegratedSenseVoiceASRSystem(sensevoice_model="iic/SenseVoiceSmall")
    
    # 初始化
    if not asr_system.initialize():
        print("系统初始化失败")
        return
    
    # 处理音频
    result = asr_system.process_audio(audio_file)
    
    if result:
        # 显示结果
        print(f"\n处理结果:")
        print(f"音频文件: {result['audio_info']['file_path']}")
        print(f"总时长: {result['audio_info']['total_duration']:.2f} 秒")
        print(f"主要说话人时长: {result['audio_info']['main_speaker_duration']:.2f} 秒")
        print(f"主要说话人占比: {result['audio_info']['main_speaker_percentage']:.1f}%")
        print(f"处理时间: {result['system_info']['processing_time']:.2f} 秒")
        
        print(f"\n说话人分离:")
        print(f"检测到说话人数: {result['speaker_diarization']['num_speakers']}")
        print(f"主要说话人: {result['speaker_diarization']['main_speaker']}")
        
        print(f"\nSenseVoice转录结果:")
        print(f"有效片段数: {result['transcription']['segment_count']}")
        print(f"总词数: {result['transcription']['total_words']}")
        print(f"ASR模型: {result['system_info']['asr_model']}")
        
        # 显示SenseVoice特色功能
        if result['sensevoice_features']['emotions_detected']:
            print(f"检测到的情感: {', '.join(result['sensevoice_features']['emotions_detected'])}")
        
        if result['sensevoice_features']['events_detected']:
            print(f"检测到的事件: {', '.join(result['sensevoice_features']['events_detected'])}")
        
        if result['transcription']['segments']:
            print(f"\n主要说话人详细转录:")
            print("-" * 60)
            for i, segment in enumerate(result['transcription']['segments'], 1):
                if segment.get('text'):
                    emotion_info = f" [{segment.get('emotion', '')}]" if segment.get('emotion') else ""
                    print(f"{i:2d}. [{segment['start_time']:6.1f}s - {segment['end_time']:6.1f}s] {segment['text']}{emotion_info}")
            print("-" * 60)
            
            print(f"\n主要说话人完整文本:")
            print("=" * 60)
            print(result['transcription']['full_text'])
            print("=" * 60)
        
        # 保存结果
        output_file = f"integrated_sensevoice_asr_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        print(f"\n详细结果已保存到: {output_file}")
        
    else:
        print("处理失败")

if __name__ == "__main__":
    main()

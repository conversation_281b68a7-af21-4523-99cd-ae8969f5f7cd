#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Whisper Large-v3下载脚本
避免网络超时问题
"""

import os
import sys
import logging
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def simple_download_v3():
    """简单下载Large-v3模型"""
    try:
        print("=" * 60)
        print("简单Whisper Large-v3下载")
        print("=" * 60)

        # 检查是否已有模型
        model_path = os.path.expanduser("~/.cache/huggingface/hub/models--Systran--faster-whisper-large-v3")
        if os.path.exists(model_path):
            print(f"✓ Large-v3模型已存在: {model_path}")
            return True

        # 尝试下载v3
        print("开始下载Large-v3模型...")
        print("如果下载卡住，请按Ctrl+C中断，然后使用Large-v2")

        try:
            from faster_whisper import WhisperModel

            # 设置超时环境变量
            os.environ['HF_HUB_DOWNLOAD_TIMEOUT'] = '300'  # 5分钟超时

            # 简单配置下载
            model = WhisperModel("large-v3", device="cpu")
            print("✓ Large-v3模型下载成功")
            return True

        except KeyboardInterrupt:
            print("\n⚠ 下载被用户中断")
            print("建议使用Large-v2模型作为替代")
            return False

        except Exception as e:
            print(f"✗ 下载失败: {e}")
            print("建议使用Large-v2模型作为替代")
            return False

    except Exception as e:
        print(f"✗ 下载过程出错: {e}")
        return False

def check_available_models():
    """检查可用的模型"""
    print("\n检查可用模型:")

    models_to_check = [
        ("large-v3", "~/.cache/huggingface/hub/models--Systran--faster-whisper-large-v3"),
        ("large-v2", "~/.cache/huggingface/hub/models--Systran--faster-whisper-large-v2"),
        ("large", "~/.cache/huggingface/hub/models--Systran--faster-whisper-large"),
        ("medium", "~/.cache/huggingface/hub/models--Systran--faster-whisper-medium")
    ]

    available_models = []

    for model_name, model_path in models_to_check:
        expanded_path = os.path.expanduser(model_path)
        if os.path.exists(expanded_path):
            print(f"✓ {model_name}: {expanded_path}")
            available_models.append(model_name)
        else:
            print(f"✗ {model_name}: 未安装")

    return available_models

def main():
    """主函数"""
    print("Whisper Large-v3简单下载工具")

    # 检查现有模型
    available = check_available_models()

    if "large-v3" in available:
        print("\n🎉 Large-v3已可用！")
        print("可以直接运行ASR系统")
    elif "large-v2" in available:
        print("\n✓ Large-v2可用")
        print("建议升级到Large-v3以获得更好性能")

        choice = input("\n是否尝试下载Large-v3? (y/n): ").lower()
        if choice == 'y':
            simple_download_v3()
        else:
            print("继续使用Large-v2模型")
    else:
        print("\n需要下载模型...")
        simple_download_v3()

    print("\n" + "="*60)
    print("下载完成")
    print("="*60)

    # 最终检查
    final_available = check_available_models()

    if "large-v3" in final_available:
        print("🎉 推荐使用Large-v3模型")
        print("运行: python integrated_asr_system.py")
    elif "large-v2" in final_available:
        print("✓ 可以使用Large-v2模型")
        print("运行: python integrated_asr_system.py")
        print("(系统会自动使用可用的最佳模型)")
    else:
        print("❌ 没有可用的大模型")
        print("请检查网络连接或使用较小的模型")

if __name__ == "__main__":
    main()

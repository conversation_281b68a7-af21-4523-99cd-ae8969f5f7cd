#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的ASR系统
专注于主要说话人（录音笔主人）的高质量语音识别
"""

import os
import sys
import logging
import warnings
import numpy as np
import json
from datetime import datetime
from pathlib import Path

# 抑制警告
warnings.filterwarnings("ignore")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入依赖
try:
    from faster_whisper import WhisperModel
    WHISPER_AVAILABLE = True
    logger.info("Faster-Whisper可用")
except ImportError:
    WHISPER_AVAILABLE = False
    logger.error("Faster-Whisper未安装")

class ImprovedASRSystem:
    """改进的ASR系统"""

    def __init__(self, model_size="large-v3"):
        self.model_size = model_size
        self.model = None
        self.device = "cpu"
        self.compute_type = "int8"  # 在Mac上使用int8以节省内存

        # 优化的参数 - 针对Large-v3调优
        self.beam_size = 5
        self.best_of = 5
        self.temperature = [0.0, 0.2, 0.4, 0.6, 0.8, 1.0]  # 多温度采样

        logger.info(f"初始化改进ASR系统 (模型: {model_size})")

    def initialize(self):
        """初始化Whisper模型"""
        try:
            if not WHISPER_AVAILABLE:
                raise ImportError("需要安装faster-whisper")

            logger.info(f"正在加载Whisper模型: {self.model_size}")

            # 检查本地模型缓存
            local_model_paths = {
                "large-v2": "~/.cache/huggingface/hub/models--Systran--faster-whisper-large-v2",
                "large-v3": "~/.cache/huggingface/hub/models--Systran--faster-whisper-large-v3"
            }

            model_path = local_model_paths.get(self.model_size)
            if model_path:
                expanded_path = os.path.expanduser(model_path)
                if os.path.exists(expanded_path):
                    logger.info(f"使用本地路径加载{self.model_size}模型")
                    self.model = WhisperModel(
                        expanded_path,
                        device=self.device,
                        compute_type=self.compute_type,
                        cpu_threads=4
                    )
                else:
                    logger.info(f"本地{self.model_size}模型不存在，从HuggingFace下载")
                    self.model = WhisperModel(
                        self.model_size,
                        device=self.device,
                        compute_type=self.compute_type,
                        cpu_threads=4
                    )
            else:
                # 其他模型直接下载
                self.model = WhisperModel(
                    self.model_size,
                    device=self.device,
                    compute_type=self.compute_type,
                    cpu_threads=4
                )

            logger.info("Whisper模型加载成功")
            return True

        except Exception as e:
            logger.error(f"初始化Whisper模型失败: {e}")
            return False

    def transcribe_with_high_quality(self, audio_path: str):
        """高质量转录"""
        try:
            if self.model is None:
                raise RuntimeError("模型未初始化")

            logger.info(f"开始高质量转录: {audio_path}")

            # 使用优化参数进行转录
            segments, info = self.model.transcribe(
                audio_path,
                language="zh",  # 中文
                beam_size=self.beam_size,
                best_of=self.best_of,
                temperature=self.temperature,
                word_timestamps=True,
                vad_filter=True,
                vad_parameters=dict(
                    min_silence_duration_ms=300,  # 减少最小静音时长
                    speech_pad_ms=400,            # 增加语音填充
                    max_speech_duration_s=30     # 最大语音段时长
                ),
                condition_on_previous_text=True,  # 基于前文进行条件生成
                compression_ratio_threshold=2.4,  # 压缩比阈值
                log_prob_threshold=-1.0,          # 对数概率阈值
                no_speech_threshold=0.6           # 无语音阈值
            )

            # 收集所有片段
            all_segments = []
            full_text = ""

            for segment in segments:
                segment_info = {
                    'start': segment.start,
                    'end': segment.end,
                    'duration': segment.end - segment.start,
                    'text': segment.text.strip(),
                    'avg_logprob': segment.avg_logprob,
                    'no_speech_prob': segment.no_speech_prob,
                    'compression_ratio': getattr(segment, 'compression_ratio', 0),
                    'temperature': getattr(segment, 'temperature', 0)
                }

                # 添加词级时间戳
                if hasattr(segment, 'words') and segment.words:
                    segment_info['words'] = [
                        {
                            'word': word.word.strip(),
                            'start': word.start,
                            'end': word.end,
                            'probability': word.probability
                        }
                        for word in segment.words
                    ]

                all_segments.append(segment_info)
                full_text += segment.text

            result = {
                'text': full_text.strip(),
                'language': info.language,
                'language_probability': info.language_probability,
                'duration': info.duration,
                'segments': all_segments,
                'total_segments': len(all_segments)
            }

            logger.info(f"转录完成，检测到 {len(all_segments)} 个语音片段")
            logger.info(f"音频时长: {info.duration:.2f}秒")
            logger.info(f"检测语言: {info.language} (置信度: {info.language_probability:.3f})")

            return result

        except Exception as e:
            logger.error(f"转录失败: {e}")
            return None

class MainSpeakerIdentifier:
    """主要说话人识别器"""

    def __init__(self):
        logger.info("初始化主要说话人识别器")

    def identify_main_speaker(self, segments):
        """识别主要说话人"""
        if not segments:
            return []

        # 基于时间间隔进行说话人分组
        speaker_groups = self._group_by_speaker(segments)

        # 找出主要说话人（说话时间最长）
        main_speaker = self._find_main_speaker(speaker_groups)

        # 返回主要说话人的片段
        main_speaker_segments = speaker_groups.get(main_speaker, [])

        logger.info(f"识别出主要说话人: {main_speaker}")
        logger.info(f"主要说话人片段数: {len(main_speaker_segments)}")

        return main_speaker_segments

    def _group_by_speaker(self, segments):
        """基于时间间隔分组说话人"""
        speaker_groups = {}
        current_speaker = 0
        last_end_time = 0

        for segment in segments:
            start_time = segment['start']

            # 计算时间间隔
            time_gap = start_time - last_end_time

            # 如果时间间隔超过阈值，认为是新说话人
            if time_gap > 2.0:  # 2秒间隔
                current_speaker += 1

            speaker_id = f"SPEAKER_{current_speaker:02d}"

            if speaker_id not in speaker_groups:
                speaker_groups[speaker_id] = []

            # 添加说话人信息
            segment_with_speaker = segment.copy()
            segment_with_speaker['speaker'] = speaker_id
            segment_with_speaker['time_gap'] = time_gap

            speaker_groups[speaker_id].append(segment_with_speaker)
            last_end_time = segment['end']

        return speaker_groups

    def _find_main_speaker(self, speaker_groups):
        """找出主要说话人（说话时间最长）"""
        speaker_durations = {}

        for speaker_id, segments in speaker_groups.items():
            total_duration = sum([s['duration'] for s in segments])
            speaker_durations[speaker_id] = total_duration

        # 返回说话时间最长的说话人
        main_speaker = max(speaker_durations.keys(), key=speaker_durations.get)

        logger.info("说话人时长统计:")
        for speaker, duration in speaker_durations.items():
            status = " (主要说话人)" if speaker == main_speaker else ""
            logger.info(f"  {speaker}: {duration:.2f}秒{status}")

        return main_speaker

class QualityFilter:
    """质量过滤器"""

    def __init__(self):
        self.min_confidence = -1.2  # 最低置信度阈值
        self.max_no_speech_prob = 0.8  # 最大无语音概率
        self.min_duration = 0.3  # 最短片段时长

        logger.info("初始化质量过滤器")

    def filter_segments(self, segments):
        """过滤低质量片段"""
        filtered_segments = []

        for segment in segments:
            # 检查置信度
            if segment.get('avg_logprob', 0) < self.min_confidence:
                continue

            # 检查无语音概率
            if segment.get('no_speech_prob', 1) > self.max_no_speech_prob:
                continue

            # 检查时长
            if segment.get('duration', 0) < self.min_duration:
                continue

            # 检查文本内容
            text = segment.get('text', '').strip()
            if len(text) < 2:  # 至少2个字符
                continue

            filtered_segments.append(segment)

        logger.info(f"质量过滤: {len(segments)} -> {len(filtered_segments)} 片段")
        return filtered_segments

def main():
    """主函数"""
    print("=" * 80)
    print("改进的机场地勤ASR系统")
    print("专注于主要说话人的高质量语音识别")
    print("=" * 80)

    # 检查音频文件
    audio_file = "机场地勤音频.WAV"
    if not os.path.exists(audio_file):
        print(f"错误: 音频文件不存在 - {audio_file}")
        return

    try:
        # 创建系统组件
        asr_system = ImprovedASRSystem(model_size="large-v3")  # 使用large-v3模型，最新最高准确性
        speaker_identifier = MainSpeakerIdentifier()
        quality_filter = QualityFilter()

        # 初始化ASR系统
        if not asr_system.initialize():
            print("错误: ASR系统初始化失败")
            return

        print(f"开始处理音频文件: {audio_file}")
        start_time = datetime.now()

        # 1. 高质量转录
        transcription_result = asr_system.transcribe_with_high_quality(audio_file)
        if not transcription_result:
            print("转录失败")
            return

        # 2. 识别主要说话人
        main_speaker_segments = speaker_identifier.identify_main_speaker(
            transcription_result['segments']
        )

        # 3. 质量过滤
        filtered_segments = quality_filter.filter_segments(main_speaker_segments)

        # 4. 整理结果
        processing_time = (datetime.now() - start_time).total_seconds()

        # 计算统计信息
        total_duration = sum([s['duration'] for s in filtered_segments])
        total_words = sum([len(s['text'].split()) for s in filtered_segments])
        avg_confidence = np.mean([s['avg_logprob'] for s in filtered_segments]) if filtered_segments else 0

        # 生成完整文本
        full_text = ' '.join([s['text'] for s in filtered_segments])

        # 显示结果
        print(f"\n处理结果:")
        print(f"音频文件: {audio_file}")
        print(f"文件大小: {os.path.getsize(audio_file) / (1024*1024):.2f} MB")
        print(f"音频时长: {transcription_result['duration']:.2f} 秒")
        print(f"处理时间: {processing_time:.2f} 秒")
        print(f"使用模型: {asr_system.model_size}")
        print(f"检测语言: {transcription_result['language']} (置信度: {transcription_result['language_probability']:.3f})")

        print(f"\n主要说话人分析:")
        print(f"有效片段数: {len(filtered_segments)}")
        print(f"说话总时长: {total_duration:.2f} 秒")
        print(f"总词数: {total_words}")
        print(f"平均置信度: {avg_confidence:.3f}")
        print(f"语速: {total_words/total_duration:.2f} 词/秒" if total_duration > 0 else "语速: N/A")

        if filtered_segments:
            print(f"\n主要说话人详细转录:")
            print("-" * 60)
            for i, segment in enumerate(filtered_segments, 1):
                print(f"{i:2d}. [{segment['start']:6.1f}s - {segment['end']:6.1f}s] "
                      f"(置信度: {segment['avg_logprob']:6.3f})")
                print(f"    {segment['text']}")
            print("-" * 60)

            print(f"\n主要说话人完整文本:")
            print("=" * 60)
            print(full_text)
            print("=" * 60)
        else:
            print("\n未检测到有效的主要说话人内容")

        # 保存结果
        result = {
            'system_info': {
                'version': '改进ASR系统 v2.0',
                'model': asr_system.model_size,
                'processing_time': processing_time,
                'timestamp': datetime.now().isoformat()
            },
            'audio_info': {
                'file_path': audio_file,
                'duration': transcription_result['duration'],
                'language': transcription_result['language'],
                'language_probability': transcription_result['language_probability']
            },
            'main_speaker_result': {
                'segments': filtered_segments,
                'segment_count': len(filtered_segments),
                'total_duration': total_duration,
                'total_words': total_words,
                'avg_confidence': avg_confidence,
                'full_text': full_text
            }
        }

        output_file = f"improved_asr_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        print(f"\n详细结果已保存到: {output_file}")

    except Exception as e:
        print(f"错误: {e}")
        logger.error(f"程序执行失败: {e}")

if __name__ == "__main__":
    main()

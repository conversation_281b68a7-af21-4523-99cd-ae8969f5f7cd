#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用镜像源的pyannote说话人分离系统
解决网络连接问题
"""

import os
import sys
import logging
import warnings
from datetime import datetime

# 抑制警告
warnings.filterwarnings("ignore")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# HuggingFace Token
HF_TOKEN = "*************************************"

# 设置环境变量使用镜像源
os.environ['HF_TOKEN'] = HF_TOKEN
os.environ['HF_ENDPOINT'] = 'https://hf-mirror.com'
os.environ['HF_HUB_ENDPOINT'] = 'https://hf-mirror.com'

class MirrorPyAnnoteDiarization:
    """使用镜像源的pyannote说话人分离系统"""
    
    def __init__(self):
        self.pipeline = None
        self.model_name = None
        logger.info("初始化镜像源pyannote说话人分离系统")
    
    def test_network_connection(self):
        """测试网络连接"""
        import requests
        
        endpoints = [
            'https://hf-mirror.com',
            'https://huggingface.co'
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(f"{endpoint}/api/whoami", 
                                      headers={"Authorization": f"Bearer {HF_TOKEN}"},
                                      timeout=10)
                if response.status_code == 200:
                    logger.info(f"✅ {endpoint} 连接成功")
                    return endpoint
                else:
                    logger.warning(f"❌ {endpoint} 返回状态码: {response.status_code}")
            except Exception as e:
                logger.warning(f"❌ {endpoint} 连接失败: {e}")
        
        return None
    
    def initialize_with_mirror(self):
        """使用镜像源初始化"""
        try:
            from pyannote.audio import Pipeline
            from huggingface_hub import login
            
            # 登录HuggingFace
            login(token=HF_TOKEN)
            logger.info("HuggingFace登录成功")
            
            # 测试网络连接
            working_endpoint = self.test_network_connection()
            if not working_endpoint:
                logger.error("无法连接到任何HuggingFace端点")
                return False
            
            logger.info(f"使用端点: {working_endpoint}")
            
            # 尝试加载模型
            models_to_try = [
                "pyannote/speaker-diarization-3.1",
                "pyannote/speaker-diarization-3.0", 
                "pyannote/speaker-diarization"
            ]
            
            for model_name in models_to_try:
                try:
                    logger.info(f"尝试从镜像源加载: {model_name}")
                    
                    # 使用已保存的token
                    self.pipeline = Pipeline.from_pretrained(
                        model_name,
                        use_auth_token=True
                    )
                    
                    if self.pipeline is not None:
                        self.model_name = model_name
                        logger.info(f"✅ 成功加载模型: {model_name}")
                        return True
                        
                except Exception as e:
                    logger.warning(f"❌ {model_name} 加载失败: {e}")
                    continue
            
            logger.error("所有模型都无法加载")
            return False
            
        except ImportError as e:
            logger.error(f"导入错误: {e}")
            return False
        except Exception as e:
            logger.error(f"初始化错误: {e}")
            return False
    
    def manual_download_and_load(self):
        """手动下载并加载模型"""
        try:
            from huggingface_hub import snapshot_download
            import tempfile
            
            logger.info("尝试手动下载模型...")
            
            # 创建临时目录
            temp_dir = tempfile.mkdtemp()
            logger.info(f"临时目录: {temp_dir}")
            
            models_to_try = [
                "pyannote/speaker-diarization",
                "pyannote/speaker-diarization-3.0",
                "pyannote/speaker-diarization-3.1"
            ]
            
            for model_name in models_to_try:
                try:
                    logger.info(f"手动下载: {model_name}")
                    
                    # 下载模型文件
                    local_dir = snapshot_download(
                        repo_id=model_name,
                        token=HF_TOKEN,
                        local_dir=f"{temp_dir}/{model_name.replace('/', '--')}",
                        local_dir_use_symlinks=False
                    )
                    
                    logger.info(f"✅ 下载成功: {local_dir}")
                    
                    # 尝试从本地加载
                    from pyannote.audio import Pipeline
                    self.pipeline = Pipeline.from_pretrained(local_dir)
                    
                    if self.pipeline is not None:
                        self.model_name = model_name
                        logger.info(f"✅ 从本地加载成功: {model_name}")
                        return True
                        
                except Exception as e:
                    logger.warning(f"❌ {model_name} 手动下载失败: {e}")
                    continue
            
            return False
            
        except Exception as e:
            logger.error(f"手动下载过程出错: {e}")
            return False
    
    def initialize(self):
        """初始化pipeline"""
        logger.info("开始初始化...")
        
        # 方法1: 使用镜像源
        if self.initialize_with_mirror():
            return True
        
        logger.info("镜像源方法失败，尝试手动下载...")
        
        # 方法2: 手动下载
        if self.manual_download_and_load():
            return True
        
        logger.error("所有初始化方法都失败")
        return False
    
    def process_audio(self, audio_path):
        """处理音频文件"""
        try:
            if not self.pipeline:
                raise RuntimeError("Pipeline未初始化")
            
            logger.info(f"开始处理音频: {audio_path}")
            start_time = datetime.now()
            
            # 执行说话人分离
            diarization = self.pipeline(audio_path)
            
            # 分析结果
            speakers = list(diarization.labels())
            num_speakers = len(speakers)
            
            logger.info(f"检测到 {num_speakers} 个说话人: {speakers}")
            
            # 计算说话人统计
            speaker_durations = {}
            speaker_segments = {}
            
            for speaker in speakers:
                speaker_durations[speaker] = 0.0
                speaker_segments[speaker] = []
            
            for segment, _, speaker in diarization.itertracks(yield_label=True):
                duration = segment.end - segment.start
                speaker_durations[speaker] += duration
                speaker_segments[speaker].append((segment.start, segment.end))
            
            # 确定主要说话人
            main_speaker = max(speaker_durations.items(), key=lambda x: x[1])[0]
            main_speaker_segments = self._merge_close_segments(speaker_segments[main_speaker])
            
            # 获取音频总时长
            audio_duration = max([segment.end for segment, _, _ in diarization.itertracks(yield_label=True)])
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = {
                'success': True,
                'model_used': self.model_name,
                'num_speakers': num_speakers,
                'speakers': speakers,
                'main_speaker': main_speaker,
                'speaker_durations': speaker_durations,
                'main_speaker_segments': main_speaker_segments,
                'audio_duration': audio_duration,
                'processing_time': processing_time,
                'raw_diarization': diarization
            }
            
            logger.info(f"✅ 处理完成，用时 {processing_time:.2f} 秒")
            return result
            
        except Exception as e:
            logger.error(f"❌ 处理失败: {e}")
            return None
    
    def _merge_close_segments(self, segments, gap_threshold=1.0):
        """合并相近的片段"""
        if not segments:
            return []
        
        sorted_segments = sorted(segments, key=lambda x: x[0])
        merged = [sorted_segments[0]]
        
        for current_start, current_end in sorted_segments[1:]:
            last_start, last_end = merged[-1]
            
            if current_start - last_end <= gap_threshold:
                merged[-1] = (last_start, max(last_end, current_end))
            else:
                merged.append((current_start, current_end))
        
        return merged

def test_mirror_pyannote():
    """测试镜像源pyannote系统"""
    print("=" * 80)
    print("测试镜像源pyannote说话人分离系统")
    print("解决网络连接问题")
    print("=" * 80)
    
    # 检查音频文件
    audio_file = "../data/机场地勤音频.WAV"
    if not os.path.exists(audio_file):
        print(f"错误: 音频文件不存在 - {audio_file}")
        return
    
    # 创建系统
    system = MirrorPyAnnoteDiarization()
    
    # 初始化
    if not system.initialize():
        print("❌ 系统初始化失败")
        return
    
    # 处理音频
    result = system.process_audio(audio_file)
    
    if result:
        print(f"\n✅ 处理成功!")
        print(f"使用模型: {result['model_used']}")
        print(f"检测到说话人数: {result['num_speakers']}")
        print(f"说话人列表: {result['speakers']}")
        print(f"主要说话人: {result['main_speaker']}")
        print(f"音频总时长: {result['audio_duration']:.2f}秒")
        print(f"处理时间: {result['processing_time']:.2f}秒")
        
        print(f"\n说话人时长统计:")
        for speaker, duration in result['speaker_durations'].items():
            percentage = (duration / result['audio_duration']) * 100
            marker = " (主要说话人)" if speaker == result['main_speaker'] else ""
            print(f"  {speaker}: {duration:.2f}秒 ({percentage:.1f}%){marker}")
        
        print(f"\n主要说话人片段:")
        for i, (start, end) in enumerate(result['main_speaker_segments'], 1):
            print(f"  片段{i}: {start:.1f}s - {end:.1f}s (时长: {end-start:.1f}s)")
    else:
        print("❌ 处理失败")

if __name__ == "__main__":
    test_mirror_pyannote()

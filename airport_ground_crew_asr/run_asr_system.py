#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机场地勤ASR系统运行脚本
自动化执行完整的ASR流程
"""

import os
import sys
import subprocess
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'asr_system_{datetime.now().strftime("%Y%m%d")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def run_command(command, description):
    """运行命令并记录结果"""
    logger.info(f"开始执行: {description}")
    logger.info(f"命令: {command}")

    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)

        if result.returncode == 0:
            logger.info(f"✅ {description} - 成功")
            if result.stdout:
                logger.info(f"输出: {result.stdout}")
            return True
        else:
            logger.error(f"❌ {description} - 失败")
            logger.error(f"错误: {result.stderr}")
            return False

    except Exception as e:
        logger.error(f"❌ {description} - 异常: {e}")
        return False

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        logger.info(f"✅ {description} - 文件存在: {file_path}")
        return True
    else:
        logger.error(f"❌ {description} - 文件不存在: {file_path}")
        return False

def main():
    """主函数"""
    print("=" * 80)
    print("机场地勤ASR系统 - 自动化运行脚本")
    print("=" * 80)

    # 切换到src目录
    src_dir = os.path.join(os.path.dirname(__file__), 'src')
    if not os.path.exists(src_dir):
        logger.error("src目录不存在")
        return False

    os.chdir(src_dir)
    logger.info(f"切换到工作目录: {src_dir}")

    # 检查音频文件
    audio_file = "../data/机场地勤音频.WAV"
    if not check_file_exists(audio_file, "测试音频文件"):
        return False

    # 步骤1: 修复Python 3.13兼容性
    logger.info("\n" + "="*50)
    logger.info("步骤1: 修复Python 3.13兼容性")
    logger.info("="*50)

    if not run_command("python fix_aifc.py", "修复aifc模块兼容性"):
        logger.warning("兼容性修复失败，但继续执行")

    # 步骤2: 安装说话人分离依赖
    logger.info("\n" + "="*50)
    logger.info("步骤2: 安装说话人分离依赖")
    logger.info("="*50)

    if not run_command("python alternative_speaker_diarization.py", "安装说话人分离依赖"):
        logger.warning("依赖安装可能失败，但继续执行")

    # 步骤3: 下载Whisper Large-v3模型
    logger.info("\n" + "="*50)
    logger.info("步骤3: 下载Whisper Large-v3模型")
    logger.info("="*50)

    if not run_command("python download_large_v3_model.py", "下载Whisper Large-v3模型"):
        logger.warning("Large-v3模型下载可能失败，尝试备用方案")
        if not run_command("python download_large_model.py", "下载Whisper Large-v2模型(备用)"):
            logger.warning("模型下载可能失败，但继续执行")

    # 步骤4: 测试大模型加载
    logger.info("\n" + "="*50)
    logger.info("步骤4: 测试大模型加载")
    logger.info("="*50)

    if not run_command("python test_large_model.py", "测试大模型加载"):
        logger.warning("模型测试失败，但继续执行")

    # 步骤5: 运行完整ASR系统
    logger.info("\n" + "="*50)
    logger.info("步骤5: 运行完整ASR系统")
    logger.info("="*50)

    # 修改integrated_asr_system.py中的音频文件路径
    logger.info("更新音频文件路径...")

    try:
        with open('integrated_asr_system.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # 替换音频文件路径
        content = content.replace('audio_file = "机场地勤音频.WAV"',
                                'audio_file = "../data/机场地勤音频.WAV"')

        with open('integrated_asr_system.py', 'w', encoding='utf-8') as f:
            f.write(content)

        logger.info("音频文件路径更新成功")

    except Exception as e:
        logger.error(f"更新音频文件路径失败: {e}")

    # 运行完整系统
    if run_command("python integrated_asr_system.py", "运行完整ASR系统"):
        logger.info("🎉 ASR系统运行成功！")

        # 移动结果文件到results目录
        results_dir = "../results"
        if not os.path.exists(results_dir):
            os.makedirs(results_dir)

        # 查找并移动结果文件
        for file in os.listdir('.'):
            if file.startswith('integrated_asr_result_') and file.endswith('.json'):
                src_path = file
                dst_path = os.path.join(results_dir, file)
                try:
                    os.rename(src_path, dst_path)
                    logger.info(f"结果文件已移动到: {dst_path}")
                except Exception as e:
                    logger.error(f"移动结果文件失败: {e}")

        return True
    else:
        logger.error("❌ ASR系统运行失败")
        return False

if __name__ == "__main__":
    success = main()

    if success:
        print("\n" + "="*80)
        print("🎉 机场地勤ASR系统运行完成！")
        print("请查看results目录中的输出结果")
        print("="*80)
    else:
        print("\n" + "="*80)
        print("❌ 系统运行过程中遇到问题")
        print("请查看日志文件了解详细信息")
        print("="*80)
        sys.exit(1)

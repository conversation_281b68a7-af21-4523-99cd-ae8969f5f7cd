#!/usr/bin/env python3
"""
项目打包脚本
将机场地勤ASR系统打包为可迁移的压缩包
"""

import os
import sys
import shutil
import tarfile
import zipfile
from pathlib import Path
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_package():
    """创建项目打包"""
    
    # 项目根目录
    project_root = Path(__file__).parent
    
    # 创建打包目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    package_name = f"airport_ground_crew_asr_{timestamp}"
    package_dir = project_root / package_name
    
    logger.info(f"创建打包目录: {package_dir}")
    package_dir.mkdir(exist_ok=True)
    
    # 需要打包的文件和目录
    items_to_package = [
        # 源代码
        "src/ground_crew_asr_main.py",
        "src/extract_audio_segment.py", 
        "src/speaker_diarization.py",
        
        # 文档
        "README_DEPLOYMENT.md",
        
        # 测试数据 (小文件)
        "data/机场地勤音频.WAV",
        
        # 配置文件
        "requirements.txt",
    ]
    
    # 复制文件
    for item in items_to_package:
        src_path = project_root / item
        if src_path.exists():
            # 创建目标目录
            dst_path = package_dir / item
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            if src_path.is_file():
                shutil.copy2(src_path, dst_path)
                logger.info(f"复制文件: {item}")
            elif src_path.is_dir():
                shutil.copytree(src_path, dst_path, dirs_exist_ok=True)
                logger.info(f"复制目录: {item}")
        else:
            logger.warning(f"文件不存在，跳过: {item}")
    
    # 创建requirements.txt
    requirements_content = """# 机场地勤ASR系统依赖包
faster-whisper>=0.10.0
pyannote-audio>=3.1.0
torch>=2.0.0
torchaudio>=2.0.0
pydub>=0.25.0
librosa>=0.10.0
soundfile>=0.12.0
numpy>=1.24.0
scipy>=1.10.0
matplotlib>=3.7.0
"""
    
    requirements_path = package_dir / "requirements.txt"
    with open(requirements_path, 'w', encoding='utf-8') as f:
        f.write(requirements_content)
    logger.info("创建 requirements.txt")
    
    # 创建安装脚本
    install_script = """#!/bin/bash
# 机场地勤ASR系统安装脚本

echo "开始安装机场地勤ASR系统..."

# 检查Python版本
python_version=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "Python版本: $python_version"

if [[ $(echo "$python_version >= 3.8" | bc -l) -eq 0 ]]; then
    echo "错误: 需要Python 3.8或更高版本"
    exit 1
fi

# 创建虚拟环境
echo "创建虚拟环境..."
python3 -m venv venv
source venv/bin/activate

# 升级pip
echo "升级pip..."
pip install --upgrade pip

# 安装依赖
echo "安装依赖包..."
pip install -r requirements.txt

# 检查GPU支持
echo "检查GPU支持..."
python3 -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}'); print(f'GPU数量: {torch.cuda.device_count()}')"

# 设置环境变量
echo "设置环境变量..."
echo 'export HF_ENDPOINT=https://hf-mirror.com' >> ~/.bashrc

echo "安装完成！"
echo "使用方法:"
echo "  source venv/bin/activate"
echo "  cd src"
echo "  python ground_crew_asr_main.py ../data/音频文件.wav --fast-mode"
"""
    
    install_script_path = package_dir / "install.sh"
    with open(install_script_path, 'w', encoding='utf-8') as f:
        f.write(install_script)
    os.chmod(install_script_path, 0o755)
    logger.info("创建 install.sh")
    
    # 创建GPU优化脚本
    gpu_optimize_script = """#!/usr/bin/env python3
\"\"\"
GPU优化脚本
自动检测GPU并优化配置
\"\"\"

import torch
import os

def optimize_for_gpu():
    print("=== GPU环境检测 ===")
    
    # 检查CUDA
    cuda_available = torch.cuda.is_available()
    print(f"CUDA可用: {cuda_available}")
    
    if cuda_available:
        gpu_count = torch.cuda.device_count()
        print(f"GPU数量: {gpu_count}")
        
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
        
        # 设置GPU优化
        print("\\n=== 应用GPU优化 ===")
        
        # 修改ground_crew_asr_main.py
        main_file = "src/ground_crew_asr_main.py"
        if os.path.exists(main_file):
            with open(main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换设备配置
            content = content.replace(
                'device="cpu"',
                'device="cuda" if torch.cuda.is_available() else "cpu"'
            )
            content = content.replace(
                'compute_type="int8"',
                'compute_type="float16" if torch.cuda.is_available() else "int8"'
            )
            
            with open(main_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 已优化 {main_file}")
        
        print("✅ GPU优化完成")
        print("\\n建议:")
        print("- 使用 --fast-mode 参数以获得最佳性能")
        print("- 监控GPU内存使用情况")
        print("- 如遇内存不足，可降低batch_size")
        
    else:
        print("❌ 未检测到CUDA支持")
        print("建议:")
        print("- 安装CUDA工具包")
        print("- 安装GPU版本的PyTorch")
        print("- 检查GPU驱动程序")

if __name__ == "__main__":
    optimize_for_gpu()
"""
    
    gpu_script_path = package_dir / "optimize_gpu.py"
    with open(gpu_script_path, 'w', encoding='utf-8') as f:
        f.write(gpu_optimize_script)
    logger.info("创建 optimize_gpu.py")
    
    # 创建压缩包
    logger.info("创建压缩包...")
    
    # 创建tar.gz压缩包
    tar_path = project_root / f"{package_name}.tar.gz"
    with tarfile.open(tar_path, "w:gz") as tar:
        tar.add(package_dir, arcname=package_name)
    
    # 创建zip压缩包
    zip_path = project_root / f"{package_name}.zip"
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(package_dir.parent)
                zipf.write(file_path, arc_path)
    
    # 清理临时目录
    shutil.rmtree(package_dir)
    
    # 显示结果
    logger.info("=" * 60)
    logger.info("📦 项目打包完成!")
    logger.info("=" * 60)
    logger.info(f"压缩包文件:")
    logger.info(f"  - {tar_path.name} ({tar_path.stat().st_size / 1024 / 1024:.1f}MB)")
    logger.info(f"  - {zip_path.name} ({zip_path.stat().st_size / 1024 / 1024:.1f}MB)")
    logger.info("")
    logger.info("部署步骤:")
    logger.info("1. 将压缩包传输到GPU机器")
    logger.info("2. 解压: tar -xzf airport_ground_crew_asr_*.tar.gz")
    logger.info("3. 进入目录: cd airport_ground_crew_asr_*")
    logger.info("4. 运行安装: ./install.sh")
    logger.info("5. GPU优化: python optimize_gpu.py")
    logger.info("6. 开始使用: cd src && python ground_crew_asr_main.py --help")
    logger.info("")
    logger.info("注意事项:")
    logger.info("- 大音频文件需要单独传输")
    logger.info("- 模型会在首次运行时自动下载")
    logger.info("- 建议在GPU机器上设置HF_ENDPOINT镜像")

if __name__ == "__main__":
    create_package()

# 机场地勤 ASR 系统 - 部署说明文档

## 📋 项目概述

这是一个基于 Whisper Large-v2 + pyannote 的机场地勤 ASR（自动语音识别）系统，专门用于处理地勤与乘客的对话音频。

### 🎯 主要功能

- **说话人分离**：使用 pyannote-audio 识别不同说话人
- **语音转录**：使用 Whisper Large-v2 进行中文语音识别
- **快速模式**：优化参数以提升处理速度
- **音频截取**：支持从长音频中截取指定时长片段
- **结果输出**：生成包含说话人信息和转录内容的文本文件

### 📊 性能表现

- **测试音频**：53.20 秒
- **标准模式**：220.73 秒 (4.15x 实时)
- **快速模式**：159.30 秒 (3.0x 实时) - 提升 27.8%
- **GPU 加速**：预期可提升 3-5 倍速度

## 🏗️ 系统架构

```
airport_ground_crew_asr/
├── src/                          # 源代码目录
│   ├── ground_crew_asr_main.py   # 主入口文件 (使用Large-v2)
│   ├── extract_audio_segment.py  # 音频截取工具
│   ├── speaker_diarization.py    # 说话人分离模块
│   └── GroundCrewASR/            # 输出目录
├── data/                         # 音频数据目录
│   ├── 机场地勤音频.WAV          # 测试音频 (53秒)
│   ├── 完整音频.mp3              # 完整音频 (8小时)
│   └── 完整音频_1小时.wav        # 截取的1小时音频
└── README_DEPLOYMENT.md          # 本文档
```

## 🔧 技术栈

### 核心依赖

- **Python**: 3.8+
- **faster-whisper**: Whisper 模型推理
- **pyannote-audio**: 说话人分离
- **torch**: PyTorch 深度学习框架
- **torchaudio**: 音频处理
- **pydub**: 音频格式转换
- **ffmpeg**: 音频编解码

### 模型信息

- **Whisper 模型**: Systran/faster-whisper-large-v2 (本地已下载)
- **说话人分离**: pyannote/speaker-diarization-3.1
- **模型存储**: ~/.cache/huggingface/hub/

## 🚀 部署步骤

### 1. 环境准备

```bash
# 创建Python虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install faster-whisper
pip install pyannote-audio
pip install torch torchaudio
pip install pydub
pip install librosa
pip install soundfile
```

### 2. GPU 环境配置

```bash
# 检查CUDA是否可用
python -c "import torch; print(f'CUDA available: {torch.cuda.is_available()}')"
python -c "import torch; print(f'CUDA devices: {torch.cuda.device_count()}')"

# 如果需要安装CUDA版本的PyTorch
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 3. 模型下载配置

```bash
# 设置Hugging Face镜像 (中国用户)
export HF_ENDPOINT=https://hf-mirror.com

# 或者设置代理
export HF_HUB_ENABLE_HF_TRANSFER=1
```

### 4. ffmpeg 安装

```bash
# Ubuntu/Debian
sudo apt update && sudo apt install ffmpeg

# CentOS/RHEL
sudo yum install ffmpeg

# macOS
brew install ffmpeg

# 或者使用已有的ffmpeg路径
# 修改 extract_audio_segment.py 中的 ffmpeg_path 变量
```

## 📝 使用方法

### 基本用法

```bash
cd airport_ground_crew_asr/src

# 标准模式处理音频
python ground_crew_asr_main.py ../data/音频文件.wav

# 快速模式处理音频
python ground_crew_asr_main.py ../data/音频文件.wav --fast-mode

# 指定输出目录
python ground_crew_asr_main.py ../data/音频文件.wav -o 输出目录

# 选择模型版本
python ground_crew_asr_main.py ../data/音频文件.wav -m large-v2
```

### 音频截取

```bash
# 截取前1小时音频
python extract_audio_segment.py ../data/完整音频.mp3 --duration 3600

# 从第30分钟开始截取1小时
python extract_audio_segment.py ../data/完整音频.mp3 --start 1800 --duration 3600

# 指定输出文件
python extract_audio_segment.py ../data/完整音频.mp3 -o ../data/输出.wav
```

## 🔧 GPU 优化配置

### 修改代码以启用 GPU

在 `ground_crew_asr_main.py` 中修改设备配置：

```python
# 检测GPU
device = "cuda" if torch.cuda.is_available() else "cpu"
compute_type = "float16" if device == "cuda" else "int8"

# 初始化Whisper模型
self.whisper_model = WhisperModel(
    model_path,
    device=device,           # 使用GPU
    compute_type=compute_type, # GPU使用float16
    cpu_threads=4 if device == "cpu" else 0
)
```

### pyannote GPU 配置

```python
# 在speaker_diarization.py中
import torch
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
pipeline = Pipeline.from_pretrained("pyannote/speaker-diarization-3.1")
pipeline.to(device)
```

## 📊 输出格式

### TXT 文件格式

```
音频文件: ../data/音频文件.wav
处理时间: 2025-05-27T22:04:36.476257
音频时长: 53.20秒
检测说话人数: 4
============================================================

SPEAKER_00 (时长: 7.1秒, 占比: 13.3%):
  蓝色位置是可以选择的几个人 就从俩位置就从两个位置...

SPEAKER_01 (时长: 10.5秒, 占比: 19.8%):
  是一賴沒做的 对啊你手机订单给我看一下两位在哪买的...
```

### JSON 文件格式

包含完整的系统信息、音频信息、说话人分离结果和转录结果。

## 🐛 常见问题

### 1. 模型下载失败

```bash
# 设置镜像
export HF_ENDPOINT=https://hf-mirror.com
# 或手动下载模型到指定目录
```

### 2. GPU 内存不足

```bash
# 减少batch_size或使用CPU
device = "cpu"
compute_type = "int8"
```

### 3. ffmpeg 路径问题

```bash
# 检查ffmpeg路径
which ffmpeg
# 修改extract_audio_segment.py中的ffmpeg_path
```

## 📈 性能优化建议

### GPU 环境

- 使用 CUDA 11.8+
- 显存建议 8GB+
- 启用混合精度训练

### 参数调优

- 快速模式：beam_size=1, temperature=0.0
- 标准模式：beam_size=5, temperature=[0.0, 0.2, 0.4]
- VAD 参数：根据音频质量调整

### 批处理

- 对于大量音频文件，可以实现批处理脚本
- 使用多进程并行处理

## 🔄 项目历史和上下文

### 开发历程

1. **初始版本**：使用 Whisper Large-v3，处理时间 216 秒
2. **模型切换**：改为 Large-v2，避免重复下载
3. **速度优化**：添加快速模式，提升 27.8%性能
4. **功能增强**：添加音频截取工具
5. **输出优化**：改进 TXT 格式，添加时间信息

### 用户需求

- 专注于地勤与乘客对话识别
- 需要说话人分离功能
- 要求处理速度尽可能快
- 输出格式要包含时间信息
- 支持长音频处理（8 小时）

### 技术决策

- 选择 Large-v2 而非 V3：本地已有模型，避免下载
- 使用 pyannote 而非其他方案：质量更好
- 快速模式优化：牺牲少量准确性换取速度
- 不使用说话人合并：之前实现有问题，用户回退

## 🎯 下一步计划

1. **GPU 部署**：在 GPU 机器上测试性能提升
2. **长音频处理**：处理 1 小时音频，评估效果
3. **批处理优化**：实现多文件并行处理
4. **结果分析**：根据实际效果调整参数
5. **生产部署**：优化为生产环境可用版本

## 🎯 GPU 机器部署指令

### 快速部署命令

```bash
# 1. 解压项目
tar -xzf airport_ground_crew_asr_*.tar.gz
cd airport_ground_crew_asr_*

# 2. 安装环境
./install.sh

# 3. GPU优化
python optimize_gpu.py

# 4. 测试运行
source venv/bin/activate
cd src
export HF_ENDPOINT=https://hf-mirror.com
python ground_crew_asr_main.py ../data/机场地勤音频.WAV --fast-mode

# 5. 处理1小时音频 (需要单独传输完整音频.mp3)
python extract_audio_segment.py ../data/完整音频.mp3 --duration 3600
python ground_crew_asr_main.py ../data/完整音频_segment.wav --fast-mode
```

### 关键文件说明

- `ground_crew_asr_main.py`: 主程序，已优化为使用 Large-v2 模型
- `extract_audio_segment.py`: 音频截取工具，支持从 8 小时音频截取 1 小时
- `README_DEPLOYMENT.md`: 完整部署文档
- `optimize_gpu.py`: GPU 自动优化脚本
- `install.sh`: 一键安装脚本

### 重要提醒

1. **大文件传输**: `完整音频.mp3` (8 小时) 需要单独传输到 `data/` 目录
2. **模型下载**: 首次运行会自动下载模型，设置 `HF_ENDPOINT=https://hf-mirror.com`
3. **GPU 检测**: 运行 `optimize_gpu.py` 自动配置 GPU 参数
4. **性能预期**: GPU 环境下预期速度提升 3-5 倍

### AI 助手继续开发的上下文

- 用户偏好中文回复
- 项目已从 Large-v3 回退到 Large-v2 (本地已有模型)
- 快速模式已优化，提升 27.8%性能
- 说话人合并功能已回退 (用户不满意)
- 时间段显示格式已回退 (用户不满意)
- 下一步目标: GPU 加速处理 1 小时长音频

---

**注意**：本文档包含了完整的项目上下文，可以直接提供给 AI 助手继续开发。

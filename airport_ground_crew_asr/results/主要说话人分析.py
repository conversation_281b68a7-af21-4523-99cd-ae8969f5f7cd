#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机场地勤ASR系统 - 主要说话人分析工具
分析语音识别结果，提取主要说话人并进行特殊展示
"""

import re
import json
from datetime import datetime
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np

class SpeakerAnalyzer:
    def __init__(self, input_file: str):
        """
        初始化说话人分析器
        
        Args:
            input_file: 输入的语音识别结果文件路径
        """
        self.input_file = input_file
        self.speakers_data = {}
        self.total_duration = 0
        self.audio_file = ""
        self.process_time = ""
        self.total_speakers = 0
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    def parse_file(self):
        """解析语音识别结果文件"""
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取基本信息
            audio_match = re.search(r'音频文件: (.+)', content)
            if audio_match:
                self.audio_file = audio_match.group(1)
            
            time_match = re.search(r'处理时间: (.+)', content)
            if time_match:
                self.process_time = time_match.group(1)
            
            duration_match = re.search(r'音频时长: (.+?)秒', content)
            if duration_match:
                self.total_duration = float(duration_match.group(1))
            
            speakers_match = re.search(r'检测说话人数: (\d+)', content)
            if speakers_match:
                self.total_speakers = int(speakers_match.group(1))
            
            # 解析每个说话人的信息
            speaker_pattern = r'SPEAKER_(\d+) \(时长: ([\d.]+)秒, 占比: ([\d.]+)%\):\s*(.+?)(?=SPEAKER_\d+|\Z)'
            matches = re.findall(speaker_pattern, content, re.DOTALL)
            
            for match in matches:
                speaker_id = f"SPEAKER_{match[0]}"
                duration = float(match[1])
                percentage = float(match[2])
                text = match[3].strip()
                
                self.speakers_data[speaker_id] = {
                    'duration': duration,
                    'percentage': percentage,
                    'text': text,
                    'word_count': len(text.replace(' ', ''))
                }
            
            print(f"✅ 成功解析文件: {self.input_file}")
            print(f"📊 检测到 {len(self.speakers_data)} 个说话人")
            
        except Exception as e:
            print(f"❌ 解析文件时出错: {e}")
            raise
    
    def identify_main_speaker(self) -> Tuple[str, Dict]:
        """
        识别主要说话人（说话时间最长的）
        
        Returns:
            主要说话人ID和其数据
        """
        if not self.speakers_data:
            return None, {}
        
        main_speaker_id = max(self.speakers_data.keys(), 
                             key=lambda x: self.speakers_data[x]['duration'])
        main_speaker_data = self.speakers_data[main_speaker_id]
        
        return main_speaker_id, main_speaker_data
    
    def get_other_speakers(self, main_speaker_id: str) -> Dict:
        """
        获取除主要说话人外的其他说话人数据
        
        Args:
            main_speaker_id: 主要说话人ID
            
        Returns:
            其他说话人的数据字典
        """
        return {k: v for k, v in self.speakers_data.items() if k != main_speaker_id}
    
    def generate_analysis_report(self) -> str:
        """生成分析报告"""
        if not self.speakers_data:
            return "❌ 没有可分析的数据"
        
        main_speaker_id, main_speaker_data = self.identify_main_speaker()
        other_speakers = self.get_other_speakers(main_speaker_id)
        
        # 计算其他说话人的总时长和占比
        other_total_duration = sum(data['duration'] for data in other_speakers.values())
        other_total_percentage = sum(data['percentage'] for data in other_speakers.values())
        other_total_words = sum(data['word_count'] for data in other_speakers.values())
        
        report = f"""
🎯 机场地勤ASR系统 - 主要说话人分析报告
{'='*60}

📁 音频文件: {self.audio_file}
⏰ 处理时间: {self.process_time}
🕐 音频总时长: {self.total_duration:.1f}秒 ({self.total_duration/60:.1f}分钟)
👥 检测说话人数: {self.total_speakers}

{'='*60}
🎤 主要说话人分析
{'='*60}

🔥 主要说话人: {main_speaker_id}
⏱️  说话时长: {main_speaker_data['duration']:.1f}秒 ({main_speaker_data['duration']/60:.1f}分钟)
📊 占比: {main_speaker_data['percentage']:.1f}%
📝 字数: {main_speaker_data['word_count']}字
💬 内容摘要: {main_speaker_data['text'][:100]}...

{'='*60}
👥 其他说话人汇总
{'='*60}

👤 其他说话人数量: {len(other_speakers)}
⏱️  总说话时长: {other_total_duration:.1f}秒 ({other_total_duration/60:.1f}分钟)
📊 总占比: {other_total_percentage:.1f}%
📝 总字数: {other_total_words}字

{'='*60}
📈 对比分析
{'='*60}

🎯 主要说话人 VS 其他说话人:
   时长对比: {main_speaker_data['duration']:.1f}秒 VS {other_total_duration:.1f}秒
   占比对比: {main_speaker_data['percentage']:.1f}% VS {other_total_percentage:.1f}%
   字数对比: {main_speaker_data['word_count']}字 VS {other_total_words}字
   
📊 主要说话人优势:
   时长优势: {(main_speaker_data['duration'] / other_total_duration * 100):.1f}%
   占比优势: {(main_speaker_data['percentage'] / other_total_percentage):.2f}倍

{'='*60}
🔍 详细说话人列表
{'='*60}
"""
        
        # 按说话时长排序
        sorted_speakers = sorted(self.speakers_data.items(), 
                               key=lambda x: x[1]['duration'], 
                               reverse=True)
        
        for i, (speaker_id, data) in enumerate(sorted_speakers, 1):
            status = "🔥 [主要说话人]" if speaker_id == main_speaker_id else "👤 [其他说话人]"
            report += f"""
{i}. {speaker_id} {status}
   ⏱️  时长: {data['duration']:.1f}秒 ({data['duration']/60:.1f}分钟)
   📊 占比: {data['percentage']:.1f}%
   📝 字数: {data['word_count']}字
   💬 内容: {data['text'][:150]}{'...' if len(data['text']) > 150 else ''}
"""
        
        return report
    
    def create_visualization(self, output_dir: str = "results"):
        """创建可视化图表"""
        if not self.speakers_data:
            print("❌ 没有数据可供可视化")
            return
        
        main_speaker_id, main_speaker_data = self.identify_main_speaker()
        other_speakers = self.get_other_speakers(main_speaker_id)
        
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('机场地勤ASR系统 - 主要说话人分析', fontsize=16, fontweight='bold')
        
        # 1. 主要说话人 VS 其他说话人 - 饼图
        other_total_duration = sum(data['duration'] for data in other_speakers.values())
        labels = ['主要说话人', '其他说话人']
        sizes = [main_speaker_data['duration'], other_total_duration]
        colors = ['#ff6b6b', '#4ecdc4']
        explode = (0.1, 0)  # 突出主要说话人
        
        ax1.pie(sizes, explode=explode, labels=labels, colors=colors, autopct='%1.1f%%',
                shadow=True, startangle=90)
        ax1.set_title('说话时长分布 (主要 VS 其他)', fontweight='bold')
        
        # 2. 所有说话人时长对比 - 柱状图
        speaker_ids = list(self.speakers_data.keys())
        durations = [self.speakers_data[sid]['duration'] for sid in speaker_ids]
        colors_bar = ['#ff6b6b' if sid == main_speaker_id else '#95a5a6' for sid in speaker_ids]
        
        bars = ax2.bar(range(len(speaker_ids)), durations, color=colors_bar)
        ax2.set_xlabel('说话人')
        ax2.set_ylabel('说话时长 (秒)')
        ax2.set_title('各说话人时长对比', fontweight='bold')
        ax2.set_xticks(range(len(speaker_ids)))
        ax2.set_xticklabels([sid.replace('SPEAKER_', 'S') for sid in speaker_ids], rotation=45)
        
        # 添加数值标签
        for bar, duration in zip(bars, durations):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{duration:.1f}s', ha='center', va='bottom', fontsize=8)
        
        # 3. 字数对比 - 水平柱状图
        word_counts = [self.speakers_data[sid]['word_count'] for sid in speaker_ids]
        colors_word = ['#ff6b6b' if sid == main_speaker_id else '#95a5a6' for sid in speaker_ids]
        
        bars_word = ax3.barh(range(len(speaker_ids)), word_counts, color=colors_word)
        ax3.set_ylabel('说话人')
        ax3.set_xlabel('字数')
        ax3.set_title('各说话人字数对比', fontweight='bold')
        ax3.set_yticks(range(len(speaker_ids)))
        ax3.set_yticklabels([sid.replace('SPEAKER_', 'S') for sid in speaker_ids])
        
        # 添加数值标签
        for bar, word_count in zip(bars_word, word_counts):
            width = bar.get_width()
            ax3.text(width + 10, bar.get_y() + bar.get_height()/2.,
                    f'{word_count}', ha='left', va='center', fontsize=8)
        
        # 4. 占比分布 - 环形图
        percentages = [self.speakers_data[sid]['percentage'] for sid in speaker_ids]
        colors_ring = plt.cm.Set3(np.linspace(0, 1, len(speaker_ids)))
        
        # 突出主要说话人
        main_idx = speaker_ids.index(main_speaker_id)
        colors_ring[main_idx] = np.array([1.0, 0.42, 0.42, 1.0])  # 红色
        
        wedges, texts, autotexts = ax4.pie(percentages, labels=[sid.replace('SPEAKER_', 'S') for sid in speaker_ids],
                                          colors=colors_ring, autopct='%1.1f%%',
                                          pctdistance=0.85)
        
        # 创建环形图效果
        centre_circle = plt.Circle((0,0), 0.70, fc='white')
        ax4.add_artist(centre_circle)
        ax4.set_title('说话占比分布', fontweight='bold')
        
        # 在中心添加主要说话人信息
        ax4.text(0, 0, f'{main_speaker_id}\n{main_speaker_data["percentage"]:.1f}%', 
                ha='center', va='center', fontsize=10, fontweight='bold')
        
        plt.tight_layout()
        
        # 保存图表
        import os
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chart_file = os.path.join(output_dir, f"主要说话人分析图表_{timestamp}.png")
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        print(f"📊 可视化图表已保存: {chart_file}")
        
        return chart_file
    
    def save_analysis_result(self, output_dir: str = "results"):
        """保存分析结果"""
        import os
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成报告
        report = self.generate_analysis_report()
        
        # 保存文本报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(output_dir, f"主要说话人分析报告_{timestamp}.txt")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📄 分析报告已保存: {report_file}")
        
        # 保存JSON格式的结构化数据
        main_speaker_id, main_speaker_data = self.identify_main_speaker()
        other_speakers = self.get_other_speakers(main_speaker_id)
        
        structured_data = {
            "metadata": {
                "audio_file": self.audio_file,
                "process_time": self.process_time,
                "total_duration": self.total_duration,
                "total_speakers": self.total_speakers,
                "analysis_time": datetime.now().isoformat()
            },
            "main_speaker": {
                "id": main_speaker_id,
                "data": main_speaker_data
            },
            "other_speakers": {
                "count": len(other_speakers),
                "total_duration": sum(data['duration'] for data in other_speakers.values()),
                "total_percentage": sum(data['percentage'] for data in other_speakers.values()),
                "total_words": sum(data['word_count'] for data in other_speakers.values()),
                "details": other_speakers
            },
            "all_speakers": self.speakers_data
        }
        
        json_file = os.path.join(output_dir, f"主要说话人分析数据_{timestamp}.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(structured_data, f, ensure_ascii=False, indent=2)
        
        print(f"📊 结构化数据已保存: {json_file}")
        
        return report_file, json_file

def main():
    """主函数"""
    print("🎯 机场地勤ASR系统 - 主要说话人分析工具")
    print("="*50)
    
    # 输入文件路径
    input_file = "1小时输出.json"
    
    try:
        # 创建分析器
        analyzer = SpeakerAnalyzer(input_file)
        
        # 解析文件
        analyzer.parse_file()
        
        # 生成并显示报告
        report = analyzer.generate_analysis_report()
        print(report)
        
        # 保存分析结果
        report_file, json_file = analyzer.save_analysis_result()
        
        # 创建可视化图表
        chart_file = analyzer.create_visualization()
        
        print("\n" + "="*50)
        print("✅ 分析完成！生成的文件:")
        print(f"📄 文本报告: {report_file}")
        print(f"📊 数据文件: {json_file}")
        print(f"📈 可视化图表: {chart_file}")
        
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 
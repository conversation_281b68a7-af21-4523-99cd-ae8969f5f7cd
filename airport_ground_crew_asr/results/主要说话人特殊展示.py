#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
机场地勤ASR系统 - 主要说话人特殊展示工具
突出显示主要说话人与其他说话人的对比分析
"""

import re
import json
from datetime import datetime
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from matplotlib.patches import FancyBboxPatch
import textwrap

class MainSpeakerHighlighter:
    def __init__(self, input_file: str):
        """
        初始化主要说话人突出显示器
        
        Args:
            input_file: 输入的语音识别结果文件路径
        """
        self.input_file = input_file
        self.speakers_data = {}
        self.total_duration = 0
        self.audio_file = ""
        self.process_time = ""
        self.total_speakers = 0
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    def parse_file(self):
        """解析语音识别结果文件"""
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取基本信息
            audio_match = re.search(r'音频文件: (.+)', content)
            if audio_match:
                self.audio_file = audio_match.group(1)
            
            time_match = re.search(r'处理时间: (.+)', content)
            if time_match:
                self.process_time = time_match.group(1)
            
            duration_match = re.search(r'音频时长: (.+?)秒', content)
            if duration_match:
                self.total_duration = float(duration_match.group(1))
            
            speakers_match = re.search(r'检测说话人数: (\d+)', content)
            if speakers_match:
                self.total_speakers = int(speakers_match.group(1))
            
            # 解析每个说话人的信息
            speaker_pattern = r'SPEAKER_(\d+) \(时长: ([\d.]+)秒, 占比: ([\d.]+)%\):\s*(.+?)(?=SPEAKER_\d+|\Z)'
            matches = re.findall(speaker_pattern, content, re.DOTALL)
            
            for match in matches:
                speaker_id = f"SPEAKER_{match[0]}"
                duration = float(match[1])
                percentage = float(match[2])
                text = match[3].strip()
                
                self.speakers_data[speaker_id] = {
                    'duration': duration,
                    'percentage': percentage,
                    'text': text,
                    'word_count': len(text.replace(' ', ''))
                }
            
            print(f"✅ 成功解析文件: {self.input_file}")
            print(f"📊 检测到 {len(self.speakers_data)} 个说话人")
            
        except Exception as e:
            print(f"❌ 解析文件时出错: {e}")
            raise
    
    def identify_main_speaker(self) -> Tuple[str, Dict]:
        """识别主要说话人（说话时间最长的）"""
        if not self.speakers_data:
            return None, {}
        
        main_speaker_id = max(self.speakers_data.keys(), 
                             key=lambda x: self.speakers_data[x]['duration'])
        main_speaker_data = self.speakers_data[main_speaker_id]
        
        return main_speaker_id, main_speaker_data
    
    def create_special_display(self, output_dir: str = "results"):
        """创建特殊的主要说话人展示"""
        if not self.speakers_data:
            print("❌ 没有数据可供展示")
            return
        
        main_speaker_id, main_speaker_data = self.identify_main_speaker()
        other_speakers = {k: v for k, v in self.speakers_data.items() if k != main_speaker_id}
        
        # 创建大型展示图
        fig = plt.figure(figsize=(20, 16))
        
        # 设置整体标题
        fig.suptitle('🎯 机场地勤ASR系统 - 主要说话人特殊展示', 
                    fontsize=24, fontweight='bold', y=0.95)
        
        # 创建网格布局
        gs = fig.add_gridspec(4, 3, height_ratios=[1, 2, 1.5, 1], width_ratios=[1, 2, 1])
        
        # 1. 顶部信息栏
        ax_info = fig.add_subplot(gs[0, :])
        ax_info.axis('off')
        
        info_text = f"""
📁 音频文件: {self.audio_file}  |  ⏰ 处理时间: {self.process_time}  |  🕐 总时长: {self.total_duration/60:.1f}分钟  |  👥 说话人数: {self.total_speakers}
        """
        ax_info.text(0.5, 0.5, info_text, ha='center', va='center', 
                    fontsize=14, bbox=dict(boxstyle="round,pad=0.5", facecolor='lightblue', alpha=0.7))
        
        # 2. 主要说话人突出展示 (中央大区域)
        ax_main = fig.add_subplot(gs[1, 1])
        
        # 创建主要说话人的圆形展示
        circle = plt.Circle((0.5, 0.5), 0.4, color='#ff6b6b', alpha=0.8)
        ax_main.add_patch(circle)
        
        # 添加主要说话人信息
        main_text = f"""🔥 主要说话人
{main_speaker_id}

⏱️ {main_speaker_data['duration']:.1f}秒
({main_speaker_data['duration']/60:.1f}分钟)

📊 {main_speaker_data['percentage']:.1f}%

📝 {main_speaker_data['word_count']}字"""
        
        ax_main.text(0.5, 0.5, main_text, ha='center', va='center', 
                    fontsize=16, fontweight='bold', color='white')
        
        ax_main.set_xlim(0, 1)
        ax_main.set_ylim(0, 1)
        ax_main.set_aspect('equal')
        ax_main.axis('off')
        ax_main.set_title('🎤 主要说话人', fontsize=18, fontweight='bold', pad=20)
        
        # 3. 左侧 - 主要说话人详细信息
        ax_left = fig.add_subplot(gs[1, 0])
        ax_left.axis('off')
        
        # 主要说话人内容摘要
        content_preview = main_speaker_data['text'][:200] + "..." if len(main_speaker_data['text']) > 200 else main_speaker_data['text']
        wrapped_content = textwrap.fill(content_preview, width=25)
        
        left_text = f"""💬 主要说话人内容摘要:

{wrapped_content}

📈 关键统计:
• 说话时长最长
• 内容最丰富
• 可能是地勤工作人员
• 主要负责客户服务"""
        
        ax_left.text(0.1, 0.9, left_text, ha='left', va='top', fontsize=12,
                    bbox=dict(boxstyle="round,pad=0.5", facecolor='#ffe6e6', alpha=0.8))
        
        # 4. 右侧 - 其他说话人汇总
        ax_right = fig.add_subplot(gs[1, 2])
        ax_right.axis('off')
        
        other_total_duration = sum(data['duration'] for data in other_speakers.values())
        other_total_percentage = sum(data['percentage'] for data in other_speakers.values())
        other_total_words = sum(data['word_count'] for data in other_speakers.values())
        
        right_text = f"""👥 其他说话人汇总:

👤 数量: {len(other_speakers)}人
⏱️ 总时长: {other_total_duration:.1f}秒
📊 总占比: {other_total_percentage:.1f}%
📝 总字数: {other_total_words}字

🔍 特点分析:
• 多为乘客或访客
• 说话时间较短
• 内容相对简单
• 主要是咨询和回应"""
        
        ax_right.text(0.1, 0.9, right_text, ha='left', va='top', fontsize=12,
                     bbox=dict(boxstyle="round,pad=0.5", facecolor='#e6f3ff', alpha=0.8))
        
        # 5. 对比展示区域
        ax_compare = fig.add_subplot(gs[2, :])
        
        # 创建对比柱状图
        categories = ['说话时长(秒)', '占比(%)', '字数']
        main_values = [main_speaker_data['duration'], main_speaker_data['percentage'], main_speaker_data['word_count']]
        other_values = [other_total_duration, other_total_percentage, other_total_words]
        
        x = np.arange(len(categories))
        width = 0.35
        
        bars1 = ax_compare.bar(x - width/2, main_values, width, label='🔥 主要说话人', 
                              color='#ff6b6b', alpha=0.8)
        bars2 = ax_compare.bar(x + width/2, other_values, width, label='👥 其他说话人', 
                              color='#4ecdc4', alpha=0.8)
        
        ax_compare.set_xlabel('对比维度', fontsize=14)
        ax_compare.set_ylabel('数值', fontsize=14)
        ax_compare.set_title('🎯 主要说话人 VS 其他说话人 - 全方位对比', fontsize=16, fontweight='bold')
        ax_compare.set_xticks(x)
        ax_compare.set_xticklabels(categories)
        ax_compare.legend(fontsize=12)
        
        # 添加数值标签
        for bar in bars1:
            height = bar.get_height()
            ax_compare.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                           f'{height:.1f}', ha='center', va='bottom', fontweight='bold')
        
        for bar in bars2:
            height = bar.get_height()
            ax_compare.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                           f'{height:.1f}', ha='center', va='bottom', fontweight='bold')
        
        # 6. 底部结论区域
        ax_conclusion = fig.add_subplot(gs[3, :])
        ax_conclusion.axis('off')
        
        # 计算优势比例
        duration_ratio = main_speaker_data['duration'] / other_total_duration
        word_ratio = main_speaker_data['word_count'] / other_total_words
        
        conclusion_text = f"""
🎯 分析结论:

• 主要说话人 {main_speaker_id} 是本次录音的核心人物，说话时长达 {main_speaker_data['duration']/60:.1f} 分钟，占总时长的 {main_speaker_data['percentage']:.1f}%
• 相比其他 {len(other_speakers)} 位说话人的总和，主要说话人的说话时长是其他人的 {duration_ratio:.1f} 倍
• 主要说话人的内容丰富度（字数）是其他人总和的 {word_ratio:.1f} 倍，显示其在对话中的主导地位
• 从内容分析来看，主要说话人很可能是机场地勤工作人员，负责为乘客提供各种服务和解答问题
• 其他说话人主要是乘客，进行简短的咨询、确认和回应

💡 建议: 在后续的语音分析中，可以重点关注主要说话人的服务质量和专业水平，以及乘客的满意度反馈。
        """
        
        ax_conclusion.text(0.5, 0.5, conclusion_text, ha='center', va='center', fontsize=13,
                          bbox=dict(boxstyle="round,pad=0.8", facecolor='#f0f8ff', alpha=0.9))
        
        plt.tight_layout()
        
        # 保存特殊展示图
        import os
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        display_file = os.path.join(output_dir, f"主要说话人特殊展示_{timestamp}.png")
        plt.savefig(display_file, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"🎨 特殊展示图已保存: {display_file}")
        
        return display_file
    
    def create_timeline_visualization(self, output_dir: str = "results"):
        """创建时间线可视化，显示主要说话人的分布"""
        if not self.speakers_data:
            print("❌ 没有数据可供可视化")
            return
        
        main_speaker_id, main_speaker_data = self.identify_main_speaker()
        
        # 创建时间线图
        fig, ax = plt.subplots(figsize=(16, 8))
        
        # 模拟时间分布（实际应用中需要真实的时间戳数据）
        # 这里我们根据说话时长比例来模拟分布
        total_time = self.total_duration
        current_time = 0
        
        colors = plt.cm.Set3(np.linspace(0, 1, len(self.speakers_data)))
        speaker_colors = {}
        
        # 为主要说话人设置特殊颜色
        for i, (speaker_id, data) in enumerate(self.speakers_data.items()):
            if speaker_id == main_speaker_id:
                speaker_colors[speaker_id] = '#ff6b6b'  # 红色突出
            else:
                speaker_colors[speaker_id] = colors[i]
        
        # 绘制时间线
        y_pos = 1
        segment_height = 0.8
        
        # 按时长排序来模拟时间分布
        sorted_speakers = sorted(self.speakers_data.items(), 
                               key=lambda x: x[1]['duration'], 
                               reverse=True)
        
        for speaker_id, data in sorted_speakers:
            duration = data['duration']
            
            # 绘制时间段
            rect = patches.Rectangle((current_time, y_pos - segment_height/2), 
                                   duration, segment_height,
                                   linewidth=2, 
                                   edgecolor='black' if speaker_id == main_speaker_id else 'gray',
                                   facecolor=speaker_colors[speaker_id],
                                   alpha=0.8 if speaker_id == main_speaker_id else 0.6)
            ax.add_patch(rect)
            
            # 添加标签
            if speaker_id == main_speaker_id:
                ax.text(current_time + duration/2, y_pos, 
                       f'🔥 {speaker_id}\n{duration:.1f}s\n{data["percentage"]:.1f}%',
                       ha='center', va='center', fontweight='bold', fontsize=10,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
            else:
                ax.text(current_time + duration/2, y_pos, 
                       f'{speaker_id}\n{duration:.1f}s',
                       ha='center', va='center', fontsize=8)
            
            current_time += duration
            y_pos += 1
        
        ax.set_xlim(0, total_time)
        ax.set_ylim(0, len(self.speakers_data) + 1)
        ax.set_xlabel('时间 (秒)', fontsize=14)
        ax.set_ylabel('说话人', fontsize=14)
        ax.set_title('🕐 说话人时间分布图 - 主要说话人突出显示', fontsize=16, fontweight='bold')
        
        # 添加网格
        ax.grid(True, alpha=0.3)
        
        # 添加图例
        legend_elements = [
            patches.Patch(color='#ff6b6b', label='🔥 主要说话人'),
            patches.Patch(color='#95a5a6', label='👥 其他说话人')
        ]
        ax.legend(handles=legend_elements, loc='upper right', fontsize=12)
        
        plt.tight_layout()
        
        # 保存时间线图
        import os
        os.makedirs(output_dir, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        timeline_file = os.path.join(output_dir, f"说话人时间线_{timestamp}.png")
        plt.savefig(timeline_file, dpi=300, bbox_inches='tight')
        print(f"⏰ 时间线图已保存: {timeline_file}")
        
        return timeline_file
    
    def generate_highlight_report(self) -> str:
        """生成突出主要说话人的报告"""
        if not self.speakers_data:
            return "❌ 没有可分析的数据"
        
        main_speaker_id, main_speaker_data = self.identify_main_speaker()
        other_speakers = {k: v for k, v in self.speakers_data.items() if k != main_speaker_id}
        
        other_total_duration = sum(data['duration'] for data in other_speakers.values())
        other_total_percentage = sum(data['percentage'] for data in other_speakers.values())
        other_total_words = sum(data['word_count'] for data in other_speakers.values())
        
        report = f"""
{'='*80}
🎯 机场地勤ASR系统 - 主要说话人特殊分析报告
{'='*80}

📊 核心发现:
┌─────────────────────────────────────────────────────────────────────────────┐
│  🔥 主要说话人: {main_speaker_id:<20} VS 👥 其他说话人: {len(other_speakers)}人           │
│                                                                             │
│  ⏱️  说话时长:   {main_speaker_data['duration']:>8.1f}秒 ({main_speaker_data['duration']/60:>5.1f}分钟)   VS   {other_total_duration:>8.1f}秒 ({other_total_duration/60:>5.1f}分钟)   │
│  📊 占比:       {main_speaker_data['percentage']:>8.1f}%                VS   {other_total_percentage:>8.1f}%              │
│  📝 字数:       {main_speaker_data['word_count']:>8}字                 VS   {other_total_words:>8}字               │
└─────────────────────────────────────────────────────────────────────────────┘

🎯 主要说话人优势分析:
{'─'*50}
• 时长优势: 主要说话人的说话时长是其他所有人总和的 {(main_speaker_data['duration'] / other_total_duration):.2f} 倍
• 内容优势: 主要说话人的字数是其他所有人总和的 {(main_speaker_data['word_count'] / other_total_words):.2f} 倍
• 占比优势: 主要说话人占据了近 {main_speaker_data['percentage']:.1f}% 的说话时间
• 主导地位: 在 {self.total_speakers} 个说话人中，主要说话人明显占据主导地位

🔍 角色分析:
{'─'*50}
基于说话内容和时长分析，{main_speaker_id} 很可能是:
✓ 机场地勤工作人员
✓ 客户服务代表
✓ 负责处理乘客咨询和服务的专业人员

主要说话人的典型服务内容包括:
• 航班信息查询和确认
• 座位选择和调整服务
• 票务问题处理和解答
• 客户服务和问题解决

👥 其他说话人特征:
{'─'*50}
其他 {len(other_speakers)} 位说话人主要表现为:
• 说话时间较短，多为简短回应
• 内容主要是确认、询问和感谢
• 很可能是接受服务的乘客或访客
• 与主要说话人形成明显的服务者-被服务者关系

💡 服务质量评估:
{'─'*50}
• 主要说话人说话时间长，显示其耐心和专业性
• 内容丰富，说明服务全面细致
• 与多位乘客互动，体现工作繁忙程度
• 建议进一步分析服务效率和客户满意度

📈 数据洞察:
{'─'*50}
• 总录音时长: {self.total_duration/60:.1f} 分钟
• 主要说话人活跃度: {(main_speaker_data['duration']/self.total_duration*100):.1f}%
• 平均每位其他说话人时长: {(other_total_duration/len(other_speakers)):.1f} 秒
• 服务覆盖率: 1对{len(other_speakers)} 的服务比例

{'='*80}
"""
        return report

def main():
    """主函数"""
    print("🎨 机场地勤ASR系统 - 主要说话人特殊展示工具")
    print("="*60)
    
    # 输入文件路径
    input_file = "1小时输出.json"
    
    try:
        # 创建突出显示器
        highlighter = MainSpeakerHighlighter(input_file)
        
        # 解析文件
        highlighter.parse_file()
        
        # 生成突出报告
        report = highlighter.generate_highlight_report()
        print(report)
        
        # 创建特殊展示
        display_file = highlighter.create_special_display()
        
        # 创建时间线可视化
        timeline_file = highlighter.create_timeline_visualization()
        
        # 保存报告
        import os
        os.makedirs("results", exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join("results", f"主要说话人突出报告_{timestamp}.txt")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("\n" + "="*60)
        print("✅ 特殊展示完成！生成的文件:")
        print(f"📄 突出报告: {report_file}")
        print(f"🎨 特殊展示图: {display_file}")
        print(f"⏰ 时间线图: {timeline_file}")
        
    except Exception as e:
        print(f"❌ 展示过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>🎯 机场地勤ASR系统 - 主要说话人分析展示</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Microsoft YaHei", "SimHei", Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .header {
        text-align: center;
        color: white;
        margin-bottom: 30px;
        padding: 30px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        backdrop-filter: blur(10px);
      }

      .header h1 {
        font-size: 2.5em;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      }

      .header p {
        font-size: 1.2em;
        opacity: 0.9;
      }

      .main-speaker-card {
        background: linear-gradient(135deg, #ff6b6b, #ee5a52);
        color: white;
        padding: 30px;
        border-radius: 20px;
        margin-bottom: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        text-align: center;
        position: relative;
        overflow: hidden;
      }

      .main-speaker-card::before {
        content: "";
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(
          circle,
          rgba(255, 255, 255, 0.1) 0%,
          transparent 70%
        );
        animation: pulse 3s ease-in-out infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          transform: scale(1);
          opacity: 0.5;
        }
        50% {
          transform: scale(1.1);
          opacity: 0.8;
        }
      }

      .main-speaker-card h2 {
        font-size: 2.2em;
        margin-bottom: 20px;
        position: relative;
        z-index: 1;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 20px 0;
        position: relative;
        z-index: 1;
      }

      .stat-item {
        background: rgba(255, 255, 255, 0.2);
        padding: 20px;
        border-radius: 15px;
        text-align: center;
        backdrop-filter: blur(5px);
      }

      .stat-number {
        font-size: 2em;
        font-weight: bold;
        display: block;
        margin-bottom: 5px;
      }

      .comparison-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
        margin-bottom: 30px;
      }

      .card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
      }

      .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      }

      .card h3 {
        color: #4ecdc4;
        margin-bottom: 15px;
        font-size: 1.5em;
        border-bottom: 2px solid #4ecdc4;
        padding-bottom: 10px;
      }

      .other-speakers-card h3 {
        color: #95a5a6;
        border-bottom-color: #95a5a6;
      }

      .highlight-box {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 20px;
        border-radius: 15px;
        margin: 20px 0;
        text-align: center;
      }

      .images-section {
        background: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      }

      .images-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 20px;
      }

      .image-card {
        text-align: center;
        padding: 20px;
        border: 2px solid #f0f0f0;
        border-radius: 10px;
        transition: border-color 0.3s ease;
      }

      .image-card:hover {
        border-color: #667eea;
      }

      .image-card img {
        max-width: 100%;
        height: auto;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
      }

      .image-card h4 {
        margin: 15px 0 10px 0;
        color: #333;
      }

      .conclusion {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
      }

      .conclusion h3 {
        color: #2c3e50;
        margin-bottom: 20px;
        font-size: 1.8em;
      }

      .conclusion ul {
        list-style: none;
        padding-left: 0;
      }

      .conclusion li {
        margin: 10px 0;
        padding: 10px;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 8px;
        border-left: 4px solid #3498db;
      }

      .footer {
        text-align: center;
        color: white;
        padding: 20px;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 10px;
        backdrop-filter: blur(10px);
      }

      .emoji {
        font-size: 1.2em;
        margin-right: 5px;
      }

      @media (max-width: 768px) {
        .comparison-section {
          grid-template-columns: 1fr;
        }

        .header h1 {
          font-size: 2em;
        }

        .main-speaker-card h2 {
          font-size: 1.8em;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- 页面标题 -->
      <div class="header">
        <h1>🎯 机场地勤ASR系统</h1>
        <p>主要说话人分析展示 - 突出显示核心人物与其他参与者的对比</p>
      </div>

      <!-- 主要说话人突出展示 -->
      <div class="main-speaker-card">
        <h2><span class="emoji">🔥</span>主要说话人：SPEAKER_07</h2>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-number">711.7秒</span>
            <span>说话时长</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">19.8%</span>
            <span>占比</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">4031字</span>
            <span>内容字数</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">11.9分钟</span>
            <span>说话时间</span>
          </div>
        </div>
      </div>

      <!-- 对比分析 -->
      <div class="comparison-section">
        <div class="card">
          <h3><span class="emoji">🔥</span>主要说话人特征</h3>
          <ul style="list-style: none; padding: 0">
            <li
              style="
                margin: 10px 0;
                padding: 8px;
                background: #ffe6e6;
                border-radius: 5px;
              "
            >
              <strong>角色：</strong>机场地勤工作人员
            </li>
            <li
              style="
                margin: 10px 0;
                padding: 8px;
                background: #ffe6e6;
                border-radius: 5px;
              "
            >
              <strong>职责：</strong>客户服务代表
            </li>
            <li
              style="
                margin: 10px 0;
                padding: 8px;
                background: #ffe6e6;
                border-radius: 5px;
              "
            >
              <strong>服务内容：</strong>航班咨询、座位选择、票务处理
            </li>
            <li
              style="
                margin: 10px 0;
                padding: 8px;
                background: #ffe6e6;
                border-radius: 5px;
              "
            >
              <strong>特点：</strong>说话时间长，内容丰富，专业性强
            </li>
          </ul>
        </div>

        <div class="card other-speakers-card">
          <h3><span class="emoji">👥</span>其他说话人汇总</h3>
          <ul style="list-style: none; padding: 0">
            <li
              style="
                margin: 10px 0;
                padding: 8px;
                background: #e6f3ff;
                border-radius: 5px;
              "
            >
              <strong>数量：</strong>10人
            </li>
            <li
              style="
                margin: 10px 0;
                padding: 8px;
                background: #e6f3ff;
                border-radius: 5px;
              "
            >
              <strong>总时长：</strong>761.6秒 (12.7分钟)
            </li>
            <li
              style="
                margin: 10px 0;
                padding: 8px;
                background: #e6f3ff;
                border-radius: 5px;
              "
            >
              <strong>角色：</strong>乘客或访客
            </li>
            <li
              style="
                margin: 10px 0;
                padding: 8px;
                background: #e6f3ff;
                border-radius: 5px;
              "
            >
              <strong>特点：</strong>说话简短，主要是咨询和回应
            </li>
          </ul>
        </div>
      </div>

      <!-- 关键发现 -->
      <div class="highlight-box">
        <h3><span class="emoji">📊</span>关键发现</h3>
        <p style="font-size: 1.1em; margin: 10px 0">
          主要说话人的内容丰富度（字数）是其他所有人总和的
          <strong>1.81倍</strong>， 显示其在对话中的主导地位和专业服务能力。
        </p>
      </div>

      <!-- 可视化图表展示 -->
      <div class="images-section">
        <h3 style="text-align: center; color: #2c3e50; margin-bottom: 20px">
          <span class="emoji">📈</span>可视化分析图表
        </h3>
        <div class="images-grid">
          <div class="image-card">
            <img
              src="主要说话人分析图表_20250529_103031.png"
              alt="主要说话人分析图表"
            />
            <h4>综合分析图表</h4>
            <p>包含饼图、柱状图、字数对比等多维度分析</p>
          </div>
          <div class="image-card">
            <img
              src="主要说话人特殊展示_20250529_103221.png"
              alt="主要说话人特殊展示"
            />
            <h4>特殊展示图</h4>
            <p>突出主要说话人的专门设计展示</p>
          </div>
          <div class="image-card">
            <img src="说话人时间线_20250529_103222.png" alt="说话人时间线" />
            <h4>时间线分布图</h4>
            <p>显示各说话人的时间分布情况</p>
          </div>
        </div>
      </div>

      <!-- 分析结论 -->
      <div class="conclusion">
        <h3><span class="emoji">💡</span>分析结论与建议</h3>
        <ul>
          <li>
            <strong>服务质量评估：</strong
            >主要说话人说话时间长，显示其耐心和专业性，内容丰富说明服务全面细致
          </li>
          <li>
            <strong>工作效率：</strong
            >1对10的服务比例体现了工作繁忙程度，建议进一步分析服务效率
          </li>
          <li>
            <strong>客户体验：</strong
            >其他说话人多为简短回应，建议分析客户满意度和服务体验
          </li>
          <li>
            <strong>培训建议：</strong
            >可将主要说话人的服务模式作为培训标准，提升整体服务水平
          </li>
          <li>
            <strong>系统优化：</strong
            >基于主要说话人的服务内容，优化ASR系统的识别准确度
          </li>
        </ul>
      </div>

      <!-- 页面底部 -->
      <div class="footer">
        <p><span class="emoji">🎯</span>机场地勤ASR系统 - 主要说话人分析完成</p>
        <p>生成时间：2025年5月29日 | 分析工具：Python + Matplotlib</p>
      </div>
    </div>
  </body>
</html>

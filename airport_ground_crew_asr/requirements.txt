# 机场地勤ASR系统依赖包

# 核心语音识别
faster-whisper>=0.10.0
torch>=2.0.0
torchaudio>=2.0.0

# 说话人分离
resemblyzer>=0.1.1
scikit-learn>=1.3.0
scipy>=1.10.0

# 音频处理
librosa>=0.10.0
soundfile>=0.12.0
pydub>=0.25.0
webrtcvad>=2.0.10

# 数据处理
numpy>=1.24.0
pandas>=2.0.0
matplotlib>=3.7.0

# 机器学习
umap-learn>=0.5.0
hdbscan>=0.8.0

# 系统工具
tqdm>=4.65.0
requests>=2.31.0
huggingface-hub>=0.16.0

# SenseVoice依赖（阿里巴巴语音识别模型）
funasr>=1.1.3
modelscope>=1.9.0
torch<=2.3
torchaudio>=2.0.0
gradio>=4.0.0
fastapi>=0.111.1

# 可选依赖（用于替代方案）
pyannote.audio>=3.1.0
speechbrain>=0.5.0
transformers>=4.30.0

# 开发工具
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0

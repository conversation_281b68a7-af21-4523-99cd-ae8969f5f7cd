#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import json
import re

def read_excel_data(excel_file):
    """读取Excel文件中的话术数据"""
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        print(f"成功读取Excel文件: {excel_file}")
        print(f"Excel文件包含 {len(df)} 行数据")
        print(f"Excel文件的列名: {df.columns.tolist()}")

        # 显示前几行数据以了解结构
        print("\n前5行数据:")
        print(df.head())

        return df
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        return None

def parse_current_huashu():
    """解析当前话术数据，优先从JSON文件读取原始数据"""
    # 首先尝试从JSON文件读取原始数据
    try:
        with open('0429huashu.json', 'r', encoding='utf-8') as f:
            huashu_list = json.load(f)

        # 转换格式，确保keywords是字符串而不是数组
        for item in huashu_list:
            if isinstance(item.get('keywords'), list):
                item['keywords'] = ""

        print(f"从0429huashu.json成功读取了 {len(huashu_list)} 条原始话术")
        return huashu_list

    except Exception as e:
        print(f"从JSON文件读取失败: {e}")

    # 如果JSON文件读取失败，尝试从huashu.py读取
    try:
        with open('huashu.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # 提取数据部分 - 寻找 return {'list': [ 开始
        start_pattern = "return {'list': ["
        start_idx = content.find(start_pattern)

        if start_idx == -1:
            # 尝试双引号格式
            start_pattern = 'return {"list": ['
            start_idx = content.find(start_pattern)

        if start_idx == -1:
            print("无法找到话术数据的开始位置")
            return []

        # 找到数据结束位置 - 寻找最后的 ]}
        end_pattern = "]}"
        end_idx = content.rfind(end_pattern)

        if end_idx == -1:
            print("无法找到话术数据的结束位置")
            return []

        # 提取完整的数据部分
        data_start = start_idx + len("return ")
        data_end = end_idx + len(end_pattern)
        json_str = content[data_start:data_end]

        # 使用eval安全地解析Python字典格式
        try:
            data_dict = eval(json_str)
            huashu_list = data_dict.get('list', [])
            print(f"从huashu.py成功读取了 {len(huashu_list)} 条话术")
            return huashu_list
        except Exception as eval_error:
            print(f"解析数据时出错: {eval_error}")
            # 如果eval失败，尝试使用正则表达式提取每个话术项
            return extract_huashu_with_regex(content)

    except Exception as e:
        print(f"读取huashu.py文件时出错: {e}")
        return []

def extract_huashu_with_regex(content):
    """使用正则表达式提取话术数据（备用方法）"""
    huashu_list = []

    # 匹配每个话术项的模式
    pattern = r'\{\s*"id":\s*"([^"]+)",\s*"keywords":\s*"([^"]*)",\s*"content":\s*"([^"]+)",\s*"intentCategory":\s*"([^"]+)",\s*"level":\s*"([^"]+)"\s*\}'

    matches = re.findall(pattern, content, re.DOTALL)

    for match in matches:
        huashu_item = {
            "id": match[0],
            "keywords": match[1],
            "content": match[2],
            "intentCategory": match[3],
            "level": match[4]
        }
        huashu_list.append(huashu_item)

    print(f"使用正则表达式提取了 {len(huashu_list)} 条话术")
    return huashu_list

def convert_excel_to_huashu_format(df):
    """将Excel数据转换为huashu.py格式"""
    huashu_list = []

    for index, row in df.iterrows():
        # 根据Excel的实际列名进行映射
        id_val = ""
        content = ""
        intent_category = ""
        level = ""
        keywords = ""

        # 查找ID列
        for col in ['id', 'ID', '编号', '话术编号', '意图Code']:
            if col in df.columns and pd.notna(row[col]):
                id_val = str(row[col])
                break

        # 查找内容列
        for col in ['content', '内容', '话术内容', '意图话术', '话术']:
            if col in df.columns and pd.notna(row[col]):
                content = str(row[col])
                break

        # 查找意图分类列
        for col in ['intentCategory', '意图分类', '分类', '意图名称', '类别']:
            if col in df.columns and pd.notna(row[col]):
                intent_category = str(row[col])
                break

        # 查找等级列
        for col in ['level', '等级', '级别', '强度']:
            if col in df.columns and pd.notna(row[col]):
                level = str(row[col])
                break

        # 查找关键词列
        for col in ['keywords', '关键词', '关键字']:
            if col in df.columns and pd.notna(row[col]):
                keywords = str(row[col])
                break

        # 如果没有找到必要的字段，跳过这一行
        if not content:
            continue

        # 如果没有ID，生成一个
        if not id_val:
            id_val = f"NEW-{index+1:03d}"

        # 如果没有意图分类，设置默认值
        if not intent_category:
            intent_category = "默认"

        # 如果没有等级，设置默认值
        if not level:
            level = "3"

        huashu_item = {
            "id": id_val,
            "keywords": keywords,
            "content": content,
            "intentCategory": intent_category,
            "level": level
        }

        huashu_list.append(huashu_item)

    print(f"从Excel转换了 {len(huashu_list)} 条话术")
    return huashu_list

def merge_huashu_data(current_data, new_data):
    """合并现有数据和新数据"""
    # 创建现有数据的ID集合
    existing_ids = {item['id'] for item in current_data}

    # 添加新数据，避免重复
    merged_data = current_data.copy()
    added_count = 0

    for item in new_data:
        if item['id'] not in existing_ids:
            merged_data.append(item)
            existing_ids.add(item['id'])
            added_count += 1
        else:
            print(f"跳过重复ID: {item['id']}")

    print(f"合并完成，新增 {added_count} 条话术，总计 {len(merged_data)} 条话术")
    return merged_data

def update_huashu_file(merged_data):
    """更新huashu.py文件"""
    try:
        # 生成新的文件内容
        content = "@tool\ndef main(_):\n    # 直接返回预定义的数组，已格式化\n    return {'list': [\n"

        for i, item in enumerate(merged_data):
            content += "        {\n"
            content += f'            "id": "{item["id"]}",\n'
            content += f'            "keywords": "{item["keywords"]}",\n'
            content += f'            "content": "{item["content"]}",\n'
            content += f'            "intentCategory": "{item["intentCategory"]}",\n'
            content += f'            "level": "{item["level"]}"\n'
            content += "        }"

            if i < len(merged_data) - 1:
                content += ","
            content += "\n"

        content += "    ]}\n"

        # 写入文件
        with open('huashu.py', 'w', encoding='utf-8') as f:
            f.write(content)

        print("成功更新huashu.py文件")

    except Exception as e:
        print(f"更新huashu.py文件时出错: {e}")

def main():
    """主函数"""
    excel_file = "0604话术update.xlsx"

    print("开始处理话术数据更新...")

    # 1. 读取Excel文件
    df = read_excel_data(excel_file)
    if df is None:
        return

    # 2. 解析当前huashu.py文件
    current_data = parse_current_huashu()

    # 3. 转换Excel数据为huashu格式
    new_data = convert_excel_to_huashu_format(df)

    # 4. 合并数据
    merged_data = merge_huashu_data(current_data, new_data)

    # 5. 更新huashu.py文件
    update_huashu_file(merged_data)

    print("话术数据更新完成！")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
地勤人员语音识别过滤器
区分地勤人员和乘客的语音
"""

import re
import logging

logger = logging.getLogger(__name__)

class GroundCrewRoleFilter:
    """地勤人员角色识别过滤器"""
    
    def __init__(self):
        # 地勤人员特征词汇 (主动服务、指令性语言)
        self.ground_crew_patterns = [
            # 服务指令
            r'(身份证|证件).*(给我|拿出来|出示)',
            r'(订单|手机).*(给我看|让我看|出示)',
            r'(登机牌|机票).*(打出来|出来了)',
            r'请.*(出示|提供|配合)',
            r'(您|你们?).*(需要|可以|请)',
            
            # 工作流程用语
            r'(打出来了|已经打出来)',
            r'(还有.*没.*|还差.*)',
            r'(检查|确认|核实)',
            r'(排队|等候|稍等)',
            r'(这边|那边|过来)',
            
            # 询问信息
            r'在哪.*(买的|订的|购买)',
            r'(几位|多少人|人数)',
            r'(姓名|名字).*是',
            r'(座位|位置).*安排',
            
            # 专业术语
            r'(航班|班次|起飞)',
            r'(登机|值机|办理)',
            r'(行李|托运)',
            r'(安检|检查)',
        ]
        
        # 乘客特征词汇 (被动接受、询问性语言)
        self.passenger_patterns = [
            # 询问/请求
            r'(有没有|还有.*吗|能不能)',
            r'(怎么.*|为什么|什么时候)',
            r'(可以.*吗|行不行|好不好)',
            r'(多少钱|费用|价格)',
            
            # 抱怨/担心
            r'(没了|没有了|都.*了)',
            r'(满了|坐满|没位)',
            r'(等.*|着急|赶时间)',
            r'(麻烦|不好意思|抱歉)',
            
            # 确认信息
            r'(就这.*吗|只有.*吗)',
            r'(是不是|对不对)',
            r'(我们|我的|我)',
            
            # 座位相关
            r'(俩|两个|两位)(?!.*给我看)',  # 排除"给我看两位"这种地勤用语
            r'(蓝的|红的|靠窗|过道)',
        ]
        
        # 权重设置
        self.ground_crew_weight = 2.0  # 地勤特征权重更高
        self.passenger_weight = 1.0
        
        logger.info("初始化地勤人员角色识别过滤器")
    
    def analyze_role(self, text):
        """分析文本的角色倾向"""
        if not text or len(text.strip()) < 2:
            return "unknown", 0.0
        
        text_clean = text.strip()
        
        # 计算地勤特征分数
        ground_crew_score = 0
        ground_crew_matches = []
        
        for pattern in self.ground_crew_patterns:
            matches = re.findall(pattern, text_clean, re.IGNORECASE)
            if matches:
                ground_crew_score += len(matches) * self.ground_crew_weight
                ground_crew_matches.extend(matches)
        
        # 计算乘客特征分数
        passenger_score = 0
        passenger_matches = []
        
        for pattern in self.passenger_patterns:
            matches = re.findall(pattern, text_clean, re.IGNORECASE)
            if matches:
                passenger_score += len(matches) * self.passenger_weight
                passenger_matches.extend(matches)
        
        # 特殊规则调整
        ground_crew_score += self._apply_special_rules(text_clean, "ground_crew")
        passenger_score += self._apply_special_rules(text_clean, "passenger")
        
        # 判断角色
        if ground_crew_score > passenger_score and ground_crew_score > 0:
            confidence = min(ground_crew_score / (ground_crew_score + passenger_score + 1), 0.95)
            return "ground_crew", confidence
        elif passenger_score > ground_crew_score and passenger_score > 0:
            confidence = min(passenger_score / (ground_crew_score + passenger_score + 1), 0.95)
            return "passenger", confidence
        else:
            return "unknown", 0.0
    
    def _apply_special_rules(self, text, role_type):
        """应用特殊规则"""
        bonus = 0
        
        if role_type == "ground_crew":
            # 地勤人员特殊规则
            if re.search(r'给我.*看', text):  # "给我看"是典型的地勤用语
                bonus += 1.5
            if re.search(r'.*都打出来了', text):  # "都打出来了"是地勤确认用语
                bonus += 1.5
            if re.search(r'身份证.*给我', text):  # 要求出示证件
                bonus += 2.0
            if re.search(r'(您|你们?).*', text) and len(text) > 8:  # 较长的服务用语
                bonus += 0.5
                
        elif role_type == "passenger":
            # 乘客特殊规则
            if re.search(r'就这.*吗', text):  # "就这...吗"是典型的乘客询问
                bonus += 1.5
            if re.search(r'(没了|满了).*', text):  # 表达担心的用语
                bonus += 1.0
            if re.search(r'(俩|两个)(?!.*给我)', text):  # 乘客说"俩"通常指自己
                bonus += 1.0
        
        return bonus
    
    def is_ground_crew_speech(self, text):
        """判断是否为地勤人员语音"""
        role, confidence = self.analyze_role(text)
        return role == "ground_crew" and confidence > 0.3
    
    def filter_ground_crew_segments(self, segments):
        """过滤出地勤人员的语音片段"""
        ground_crew_segments = []
        
        for segment in segments:
            text = segment.get('text', '').strip()
            if not text:
                continue
            
            role, confidence = self.analyze_role(text)
            
            # 添加角色信息到片段
            segment_with_role = segment.copy()
            segment_with_role['role'] = role
            segment_with_role['role_confidence'] = confidence
            
            if role == "ground_crew" and confidence > 0.3:
                ground_crew_segments.append(segment_with_role)
                logger.info(f"地勤语音 (置信度: {confidence:.2f}): {text}")
            elif role == "passenger":
                logger.info(f"乘客语音 (置信度: {confidence:.2f}): {text}")
            else:
                logger.info(f"未知角色: {text}")
        
        return ground_crew_segments

def test_role_filter():
    """测试角色识别过滤器"""
    filter_system = GroundCrewRoleFilter()
    
    test_cases = [
        # 地勤人员语音
        "你手机订单给我看一下 两位",
        "身份证给我",
        "李丽华都打出来了", 
        "在哪买的",
        "没打出来的那个身份证给我",
        
        # 乘客语音
        "就这俩吗",
        "就这俩蓝的 剩都没了",
        "没了就这飞机座满了吧",
        "就你俩没坐了",
        "满了",
    ]
    
    print("=" * 60)
    print("地勤人员角色识别测试")
    print("=" * 60)
    
    for text in test_cases:
        role, confidence = filter_system.analyze_role(text)
        status = "✓ 地勤" if role == "ground_crew" else "✗ 乘客" if role == "passenger" else "? 未知"
        print(f"{status} (置信度: {confidence:.2f}) - {text}")

if __name__ == "__main__":
    test_role_filter()
